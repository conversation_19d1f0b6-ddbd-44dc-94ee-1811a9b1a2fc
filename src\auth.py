import os
import json
import sys
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import tkinter as tk
from tkinter import ttk, messagebox
import webbrowser

# Import the credentials manager using a relative import
try:
    from credentials_manager import CredentialsManager
except ImportError:
    # If that fails, try with the src prefix (for when running from outside src)
    try:
        from src.credentials_manager import CredentialsManager
    except ImportError:
        # If both fail, create a simple fallback version
        class CredentialsManager:
            def __init__(self):
                pass

            def save_credentials(self, email):
                return False

            def get_credentials(self):
                return None

            def clear_credentials(self):
                return True

            def is_logged_in(self):
                return False

            def get_email(self):
                return None

class LoginDialog:
    def __init__(self, parent):
        self.parent = parent
        self.result = False

        # Initialize credentials manager
        self.credentials_manager = CredentialsManager()

        # Hardcoded configuration
        self.whatsapp_number = "+*************"
        self.spreadsheet_id = "1CRhaiBCGjodkvQi9vI5DS3y5g1pNgy9YKdtXz971Kx0"  # Your actual spreadsheet ID

        # Properly format the private key to fix padding issues
        private_key = """***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""

        # Hardcoded service account credentials with properly formatted private key
        self.credentials = {
            "type": "service_account",
            "project_id": "azanx-autoshorts",
            "private_key_id": "4290e2725f3599fa5cad66b89365a25d5e1b73ad",
            "private_key": private_key,
            "client_email": "<EMAIL>",
            "client_id": "109901492966103025795",
            "auth_uri": "https://accounts.google.com/o/oauth2/auth",
            "token_uri": "https://oauth2.googleapis.com/token",
            "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
            "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/azanxautoshorts%40azanx-autoshorts.iam.gserviceaccount.com",
            "universe_domain": "googleapis.com"
        }

        # Create the enhanced dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("1ClickVideo - Sign In")
        self.dialog.geometry("1000x700")  # Much larger size to accommodate all content
        self.dialog.resizable(False, False)

        # Make it modal
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Force focus and bring to front
        self.dialog.focus_force()
        self.dialog.lift()

        # Set modern background color (will be updated after setup)
        self.dialog.configure(bg='#0F172A')

        # Center the dialog
        self.center_dialog()

        # Create the form
        self.create_widgets()

        # Initialize Google Sheets client
        self.init_google_sheets()

        # Focus on email entry
        self.email_entry.focus_set()

        # Bind Enter key to automatic verification
        self.dialog.bind('<Return>', lambda e: self.auto_verify_on_enter())

    def center_dialog(self):
        """Center the dialog on the screen"""
        self.dialog.update_idletasks()

        # Get screen dimensions
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()

        # Calculate position
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2

        self.dialog.geometry(f"+{x}+{y}")

        # Ensure dialog stays on top
        self.dialog.attributes('-topmost', True)
        self.dialog.update()

    def create_widgets(self):
        # Define modern UI colors and components directly to avoid import issues
        self.setup_modern_ui_components()

        # Always use modern UI design
        self.create_modern_widgets()

        # Set dialog background to match modern UI
        self.dialog.configure(bg=self.ModernColors.BG_PRIMARY)

    def setup_modern_ui_components(self):
        """Define modern UI components directly to avoid import issues"""
        # Default font
        self.DEFAULT_FONT = "Segoe UI" if sys.platform == "win32" else "Helvetica"

        # Define ModernColors class
        class ModernColors:
            # Primary brand colors
            PRIMARY = "#6366F1"          # Indigo - main brand color
            PRIMARY_HOVER = "#4F46E5"    # Darker indigo for hover states
            PRIMARY_LIGHT = "#A5B4FC"    # Light indigo for accents

            # Secondary colors
            SECONDARY = "#10B981"        # Emerald green
            SECONDARY_HOVER = "#059669"  # Darker emerald

            # Accent colors
            ACCENT = "#F59E0B"          # Amber
            ACCENT_HOVER = "#D97706"    # Darker amber

            # Status colors
            SUCCESS = "#10B981"         # Green
            WARNING = "#F59E0B"         # Amber
            ERROR = "#EF4444"           # Red
            INFO = "#3B82F6"            # Blue

            # Background colors (Dark theme)
            BG_PRIMARY = "#0F172A"      # Very dark blue-gray
            BG_SECONDARY = "#1E293B"    # Dark blue-gray
            BG_TERTIARY = "#334155"     # Medium blue-gray

            # Surface colors
            SURFACE = "#1E293B"         # Card backgrounds
            SURFACE_HOVER = "#334155"   # Hover state for cards

            # Text colors
            TEXT_PRIMARY = "#F8FAFC"    # Almost white
            TEXT_SECONDARY = "#CBD5E1"  # Light gray
            TEXT_MUTED = "#64748B"      # Medium gray

            # Border colors
            BORDER = "#334155"          # Subtle borders
            BORDER_LIGHT = "#475569"    # Lighter borders for focus states

        # Store ModernColors as class attribute
        self.ModernColors = ModernColors

        # Define ModernButton class with complete implementation
        class ModernButton(tk.Button):
            """Modern button with hover effects and contemporary styling"""

            def __init__(self, parent, text="Button", command=None, style="primary", width=None, **kwargs):
                self.style_type = style
                self.parent = parent

                # Define style configurations
                styles = {
                    "primary": {
                        "bg": ModernColors.PRIMARY,
                        "fg": ModernColors.TEXT_PRIMARY,
                        "active_bg": ModernColors.PRIMARY_HOVER,
                        "border": 0
                    },
                    "secondary": {
                        "bg": ModernColors.SECONDARY,
                        "fg": ModernColors.TEXT_PRIMARY,
                        "active_bg": ModernColors.SECONDARY_HOVER,
                        "border": 0
                    },
                    "accent": {
                        "bg": ModernColors.ACCENT,
                        "fg": ModernColors.TEXT_PRIMARY,
                        "active_bg": ModernColors.ACCENT_HOVER,
                        "border": 0
                    },
                    "outline": {
                        "bg": ModernColors.BG_SECONDARY,
                        "fg": ModernColors.PRIMARY,
                        "active_bg": ModernColors.BG_TERTIARY,
                        "border": 1
                    },
                    "ghost": {
                        "bg": ModernColors.BG_SECONDARY,
                        "fg": ModernColors.TEXT_SECONDARY,
                        "active_bg": ModernColors.BG_TERTIARY,
                        "border": 0
                    }
                }

                style_config = styles.get(style, styles["primary"])

                super().__init__(
                    parent,
                    text=text,
                    command=command,
                    bg=style_config["bg"],
                    fg=style_config["fg"],
                    activebackground=style_config["active_bg"],
                    activeforeground=style_config["fg"],
                    relief=tk.FLAT,
                    borderwidth=style_config["border"],
                    font=(self.parent.DEFAULT_FONT if hasattr(self.parent, 'DEFAULT_FONT') else "Helvetica", 11, "bold"),
                    padx=20,
                    pady=10,
                    cursor="hand2",
                    width=width or 15,
                    **kwargs
                )

                # Add hover effects
                self.bind("<Enter>", self._on_enter)
                self.bind("<Leave>", self._on_leave)

                # Store original colors
                self.original_bg = style_config["bg"]
                self.hover_bg = style_config["active_bg"]

            def _on_enter(self, event):
                """Handle mouse enter"""
                if self['state'] != 'disabled':
                    self.config(bg=self.hover_bg)

            def _on_leave(self, event):
                """Handle mouse leave"""
                if self['state'] != 'disabled':
                    self.config(bg=self.original_bg)

        # Define ModernCard class
        class ModernCard(tk.Frame):
            """Modern card component with subtle shadows and rounded appearance"""
            def __init__(self, parent, title=None, padding=20, default_font=None, **kwargs):
                super().__init__(
                    parent,
                    bg=ModernColors.SURFACE,
                    relief=tk.FLAT,
                    bd=1,
                    highlightbackground=ModernColors.BORDER,
                    highlightthickness=1,
                    **kwargs
                )
                self.padding = padding
                self.default_font = default_font or "Helvetica"
                # Add internal padding
                self.configure(padx=padding, pady=padding)
                # Add title if provided
                if title:
                    title_label = tk.Label(
                        self,
                        text=title,
                        bg=ModernColors.SURFACE,
                        fg=ModernColors.TEXT_PRIMARY,
                        font=(self.default_font, 14, "bold")
                    )
                    title_label.pack(anchor="w", pady=(0, 15))

        # Store ModernCard and ModernButton as class attributes
        self.ModernCard = ModernCard
        self.ModernButton = ModernButton

    def create_modern_widgets(self):
        """Create the enhanced modern login dialog widgets"""
        # Create main container with optimized modern styling
        main_container = tk.Frame(self.dialog, bg=self.ModernColors.BG_PRIMARY)
        main_container.pack(fill=tk.BOTH, expand=True, padx=30, pady=25)

        # Optimized header section with better spacing
        header_frame = tk.Frame(main_container, bg=self.ModernColors.BG_PRIMARY)
        header_frame.pack(fill=tk.X, pady=(0, 25))

        # App title with optimized modern styling
        title_label = tk.Label(
            header_frame,
            text="1ClickVideo",
            font=(self.DEFAULT_FONT, 28, 'bold'),
            fg=self.ModernColors.PRIMARY,
            bg=self.ModernColors.BG_PRIMARY
        )
        title_label.pack(pady=(0, 8))

        # Optimized subtitle with modern styling
        subtitle_label = tk.Label(
            header_frame,
            text="Professional AI Video Creation Platform",
            font=(self.DEFAULT_FONT, 14),
            fg=self.ModernColors.TEXT_SECONDARY,
            bg=self.ModernColors.BG_PRIMARY
        )
        subtitle_label.pack(pady=(0, 15))

        # Elegant tagline
        tagline_label = tk.Label(
            header_frame,
            text="Sign in to access your creative workspace",
            font=(self.DEFAULT_FONT, 11),
            fg=self.ModernColors.TEXT_MUTED,
            bg=self.ModernColors.BG_PRIMARY
        )
        tagline_label.pack()

        # Main content area with two columns
        content_frame = tk.Frame(main_container, bg=self.ModernColors.BG_PRIMARY)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # Left column - App Info with optimized modern card
        info_card = self.ModernCard(content_frame, title="Welcome to 1ClickVideo", padding=25, default_font=self.DEFAULT_FONT)
        info_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 15))

        # Developer image and info section with optimized styling
        dev_section = tk.Frame(info_card, bg=self.ModernColors.SURFACE)
        dev_section.pack(fill=tk.X, pady=(0, 15))

        # Try to load and display the developer image
        try:
            from PIL import Image, ImageTk
            # Load and resize the image for login dialog
            img = Image.open("ghalib.jpg")
            # Resize to appropriate size for login dialog
            img = img.resize((100, 100), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(img)

            # Create image label with modern styling
            img_label = tk.Label(
                dev_section,
                image=photo,
                bg=self.ModernColors.SURFACE,
                relief=tk.FLAT,
                bd=2,
                highlightbackground=self.ModernColors.PRIMARY,
                highlightthickness=1
            )
            img_label.image = photo  # Keep a reference
            img_label.pack(side=tk.LEFT, padx=(0, 20), pady=5)
        except Exception as e:
            # If image loading fails, show a professional placeholder
            placeholder_label = tk.Label(
                dev_section,
                text="👨‍💻",
                font=(self.DEFAULT_FONT, 36),
                bg=self.ModernColors.SURFACE,
                fg=self.ModernColors.PRIMARY
            )
            placeholder_label.pack(side=tk.LEFT, padx=(0, 20), pady=5)

        # Developer info beside image
        dev_info_section = tk.Frame(dev_section, bg=self.ModernColors.SURFACE)
        dev_info_section.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        dev_name_label = tk.Label(
            dev_info_section,
            text="MD Galib Hasan",
            font=(self.DEFAULT_FONT, 12, 'bold'),
            fg=self.ModernColors.TEXT_PRIMARY,
            bg=self.ModernColors.SURFACE
        )
        dev_name_label.pack(anchor="w", pady=(5, 2))

        dev_title_label = tk.Label(
            dev_info_section,
            text="AI Video Solutions Developer",
            font=(self.DEFAULT_FONT, 10),
            fg=self.ModernColors.TEXT_SECONDARY,
            bg=self.ModernColors.SURFACE
        )
        dev_title_label.pack(anchor="w")

        # App description
        description_text = """Create professional AI-generated videos with ease. Perfect for content creators, educators, and businesses looking to produce engaging video content.

✨ Features:
• AI-powered video generation
• Professional subtitle styling
• Multiple voice options
• Custom script generation
• Modern, intuitive interface"""

        description_label = tk.Label(
            info_card,
            text=description_text,
            font=(self.DEFAULT_FONT, 11),
            fg=self.ModernColors.TEXT_PRIMARY,
            bg=self.ModernColors.SURFACE,
            wraplength=320,
            justify=tk.LEFT
        )
        description_label.pack(pady=(20, 0))

        # Right column - Optimized Login Form with modern card
        login_card = self.ModernCard(content_frame, title="Sign In", padding=25, default_font=self.DEFAULT_FONT)
        login_card.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(15, 0))

        # Optimized welcome text with modern styling
        welcome_label = tk.Label(
            login_card,
            text="Welcome Back!",
            font=(self.DEFAULT_FONT, 18, 'bold'),
            fg=self.ModernColors.TEXT_PRIMARY,
            bg=self.ModernColors.SURFACE
        )
        welcome_label.pack(pady=(15, 25))

        # Modern email input container with optimized spacing
        email_container = tk.Frame(login_card, bg=self.ModernColors.SURFACE)
        email_container.pack(fill=tk.X, pady=(0, 20))

        # Create floating label effect
        self.email_placeholder = "Enter your email address"
        self.email_focused = False

        # Email input wrapper for enhanced styling
        email_wrapper = tk.Frame(email_container, bg=self.ModernColors.SURFACE)
        email_wrapper.pack(fill=tk.X)

        # Floating label positioned above input
        self.email_label = tk.Label(
            email_wrapper,
            text="Email Address",
            font=(self.DEFAULT_FONT, 10),
            fg=self.ModernColors.TEXT_SECONDARY,
            bg=self.ModernColors.SURFACE
        )
        self.email_label.pack(anchor="w", pady=(0, 3))

        # Email input with enhanced modern styling
        self.email_entry = tk.Entry(
            email_wrapper,
            font=(self.DEFAULT_FONT, 12),
            bg=self.ModernColors.BG_TERTIARY,
            fg=self.ModernColors.TEXT_PRIMARY,
            relief=tk.FLAT,
            bd=0,
            highlightthickness=2,
            highlightcolor=self.ModernColors.PRIMARY,
            highlightbackground=self.ModernColors.BORDER,
            insertbackground=self.ModernColors.PRIMARY,
            width=35
        )
        self.email_entry.pack(fill=tk.X, ipady=12, pady=(0, 3))

        # Add placeholder text
        self.email_entry.insert(0, self.email_placeholder)
        self.email_entry.configure(fg=self.ModernColors.TEXT_MUTED)

        # Email validation indicator
        self.email_indicator = tk.Label(
            email_wrapper,
            text="",
            font=(self.DEFAULT_FONT, 9),
            fg=self.ModernColors.TEXT_MUTED,
            bg=self.ModernColors.SURFACE
        )
        self.email_indicator.pack(anchor="w")

        # Bind events for modern input behavior
        self.email_entry.bind("<FocusIn>", self.on_email_focus_in)
        self.email_entry.bind("<FocusOut>", self.on_email_focus_out)
        self.email_entry.bind("<KeyRelease>", self.on_email_change)



        # Optimized status and feedback section
        status_section = tk.Frame(login_card, bg=self.ModernColors.SURFACE)
        status_section.pack(fill=tk.X, pady=(15, 15))

        # Modern status indicator with optimized styling
        self.status_frame = tk.Frame(status_section, bg=self.ModernColors.SURFACE)
        self.status_frame.pack(fill=tk.X, pady=(0, 10))

        self.status_label = tk.Label(
            self.status_frame,
            text="💡 Enter your registered email address to access 1ClickVideo",
            font=(self.DEFAULT_FONT, 10),
            fg=self.ModernColors.TEXT_SECONDARY,
            bg=self.ModernColors.SURFACE,
            wraplength=320,
            justify=tk.CENTER
        )
        self.status_label.pack()

        # Enhanced progress indicator with smooth animation
        self.progress_frame = tk.Frame(status_section, bg=self.ModernColors.SURFACE)
        self.progress_frame.pack(fill=tk.X, pady=(0, 5))

        # Progress bar background
        progress_bg = tk.Frame(
            self.progress_frame,
            bg=self.ModernColors.BORDER,
            height=4
        )
        progress_bg.pack(fill=tk.X)

        # Animated progress bar
        self.progress_bar = tk.Frame(
            progress_bg,
            bg=self.ModernColors.PRIMARY,
            height=4
        )
        # Initially hidden - will be shown during authentication

        # Subtle divider line
        divider = tk.Frame(login_card, bg=self.ModernColors.BORDER, height=1)
        divider.pack(fill=tk.X, pady=(10, 15))

        # Modern button container with optimized spacing
        button_container = tk.Frame(login_card, bg=self.ModernColors.SURFACE)
        button_container.pack(fill=tk.X, pady=(0, 15))

        # Login button with modern styling
        self.login_btn = self.ModernButton(
            button_container,
            text="🔐 Login",
            command=self.handle_login_button,
            style="primary",
            width=15
        )
        self.login_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Purchase button with modern styling (initially visible for testing)
        self.purchase_btn = self.ModernButton(
            button_container,
            text="💳 Purchase License",
            command=self.handle_purchase_button,
            style="outline",
            width=18
        )
        self.purchase_btn.pack(side=tk.LEFT)  # Initially visible for testing

        # Track button states
        self.email_is_registered = None  # None = unknown, True = registered, False = not registered
        self.form_is_valid = False

        # Set initial button states
        self.login_btn.configure(state=tk.DISABLED)  # Disabled until form is valid
        self._last_checked_email = ""  # Track last checked email to avoid duplicate checks

        # Contact information with optimized modern styling
        contact_frame = tk.Frame(login_card, bg=self.ModernColors.SURFACE)
        contact_frame.pack(fill=tk.X, pady=(10, 0))

        contact_title = tk.Label(
            contact_frame,
            text="Need a License?",
            font=(self.DEFAULT_FONT, 10, 'bold'),
            fg=self.ModernColors.TEXT_PRIMARY,
            bg=self.ModernColors.SURFACE
        )
        contact_title.pack(pady=(0, 5))

        contact_info = tk.Label(
            contact_frame,
            text="Contact MD Galib Hasan via WhatsApp\nfor quick license purchase and activation",
            font=(self.DEFAULT_FONT, 9),
            fg=self.ModernColors.TEXT_SECONDARY,
            bg=self.ModernColors.SURFACE,
            justify=tk.CENTER
        )
        contact_info.pack()

        # WhatsApp contact link (clickable text)
        whatsapp_link = tk.Label(
            contact_frame,
            text="📱 +*************",
            font=(self.DEFAULT_FONT, 9, 'underline'),
            fg=self.ModernColors.PRIMARY,
            bg=self.ModernColors.SURFACE,
            cursor="hand2"
        )
        whatsapp_link.pack(pady=(5, 0))
        whatsapp_link.bind("<Button-1>", lambda e: self.open_whatsapp_contact())

    def create_fallback_widgets(self):
        """Fallback method if modern UI components are not available"""
        # Create main frame with two columns
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(expand=True, fill='both')

        # Left column - App Info (Blue background)
        info_frame = tk.Frame(main_frame, bg='#1a73e8', width=400)
        info_frame.pack(side='left', fill='both', expand=True)
        info_frame.pack_propagate(False)

        # App title and description
        title_label = tk.Label(
            info_frame,
            text="1ClickVideo",
            font=('Helvetica', 24, 'bold'),
            fg='white',
            bg='#1a73e8',
            wraplength=350
        )
        title_label.pack(pady=(100, 20))

        description_text = """Create professional AI Generated Videos. Perfect for content creators, educators, and businesses looking to produce engaging video content without showing their face."""

        description_label = tk.Label(
            info_frame,
            text=description_text,
            font=('Helvetica', 12),
            fg='white',
            bg='#1a73e8',
            wraplength=350,
            justify='center'
        )
        description_label.pack(pady=20, padx=25)

        # Right column - Login Form
        login_frame = tk.Frame(main_frame, bg='white', width=400)
        login_frame.pack(side='right', fill='both', expand=True)
        login_frame.pack_propagate(False)

        # Welcome text
        welcome_label = tk.Label(
            login_frame,
            text="Welcome Back!",
            font=('Helvetica', 20, 'bold'),
            bg='white'
        )
        welcome_label.pack(pady=(100, 10))

        # Email entry with label
        email_label = tk.Label(
            login_frame,
            text="Enter your email address",
            font=('Helvetica', 10),
            bg='white'
        )
        email_label.pack(pady=(20, 5))

        self.email_entry = ttk.Entry(login_frame, width=40)
        self.email_entry.pack()

        # Modern instruction text
        instruction_label = tk.Label(
            login_frame,
            text="Enter your registered email address and press Enter",
            font=('Helvetica', 11),
            bg='white',
            fg='#666666'
        )
        instruction_label.pack(pady=(15, 30))

        # Contact information for fallback
        contact_frame = tk.Frame(login_frame, bg='white')
        contact_frame.pack(pady=(30, 0))

        contact_title = tk.Label(
            contact_frame,
            text="Need a License?",
            font=('Helvetica', 12, 'bold'),
            bg='white',
            fg='#333333'
        )
        contact_title.pack(pady=(0, 10))

        contact_info = tk.Label(
            contact_frame,
            text="Contact MD Galib Hasan via WhatsApp\nfor license purchase and activation",
            font=('Helvetica', 10),
            bg='white',
            fg='#666666',
            justify=tk.CENTER
        )
        contact_info.pack()

        # WhatsApp contact (clickable)
        whatsapp_contact = tk.Label(
            contact_frame,
            text="📱 +*************",
            font=('Helvetica', 11, 'underline'),
            bg='white',
            fg='#1a73e8',
            cursor='hand2'
        )
        whatsapp_contact.pack(pady=(10, 0))
        whatsapp_contact.bind("<Button-1>", lambda e: self.open_whatsapp_contact())

    def init_google_sheets(self):
        """Initialize Google Sheets client with hardcoded credentials"""
        try:
            # Use hardcoded credentials
            scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
            creds = ServiceAccountCredentials.from_json_keyfile_dict(self.credentials, scope)
            client = gspread.authorize(creds)

            try:
                # Use hardcoded spreadsheet ID
                self.sheet = client.open_by_key(self.spreadsheet_id).sheet1
                print("Successfully connected to Google Sheets")
            except gspread.exceptions.APIError as e:
                print(f"Google Sheets API Error: {str(e)}")
                self.sheet = None
            except Exception as e:
                print(f"Error accessing spreadsheet: {str(e)}")
                self.sheet = None

        except Exception as e:
            print(f"Error initializing Google Sheets: {str(e)}")
            self.sheet = None

    def verify_login(self):
        """Verify the user's email against the Google Sheet with modern UI feedback"""
        email = self.email_entry.get().strip()

        # Handle placeholder text
        if email == self.email_placeholder:
            email = ""

        if not email:
            # Update status for modern UI
            if hasattr(self, 'status_label'):
                self.status_label.configure(
                    text="❌ Please enter your email address",
                    fg=self.ModernColors.ERROR
                )
            else:
                messagebox.showerror("Login Error", "Please enter your email address.", parent=self.dialog)
            return

        # If Google Sheets client is not initialized
        if self.sheet is None:
            # Try to reconnect once
            self.init_google_sheets()

            # If still can't connect, show an error
            if self.sheet is None:
                if hasattr(self, 'status_label'):
                    self.status_label.configure(
                        text="🌐 Unable to connect to server. Check your internet connection.",
                        fg=self.ModernColors.ERROR
                    )
                else:
                    messagebox.showerror(
                        "Error Connecting to Server",
                        "Unable to connect to authentication server. Please check your internet connection and try again.",
                        parent=self.dialog
                    )
                return

        try:
            # Get all emails from the spreadsheet
            all_emails = self.sheet.col_values(1)[1:]  # Skip header row

            if email.lower() in [e.lower() for e in all_emails]:
                # Valid email found - show success
                if hasattr(self, 'status_label'):
                    self.status_label.configure(
                        text="✅ Welcome back! Access granted.",
                        fg=self.ModernColors.SUCCESS
                    )
                    # Hide progress bar
                    if hasattr(self, 'progress_bar'):
                        self.progress_bar.pack_forget()

                # Valid email found
                self.result = True

                # Save credentials for future logins
                self.credentials_manager.save_credentials(email)

                # Small delay to show success message
                self.dialog.after(1000, self.dialog.destroy)
            else:
                # Email not found in the spreadsheet
                if hasattr(self, 'status_label'):
                    self.status_label.configure(
                        text="❌ Email not registered. Contact support for license.",
                        fg=self.ModernColors.ERROR
                    )
                    # Hide progress bar
                    if hasattr(self, 'progress_bar'):
                        self.progress_bar.pack_forget()
                else:
                    messagebox.showerror(
                        "Access Denied",
                        "Your email is not registered. Please contact support to purchase a license.",
                        parent=self.dialog
                    )
        except Exception as e:
            print(f"Error verifying email: {str(e)}")
            if hasattr(self, 'status_label'):
                self.status_label.configure(
                    text="⚠️ Error verifying account. Please try again.",
                    fg=self.ModernColors.ERROR
                )
                # Hide progress bar
                if hasattr(self, 'progress_bar'):
                    self.progress_bar.pack_forget()
            else:
                messagebox.showerror(
                    "Error Verifying Account",
                    "There was an error verifying your account. Please try again later.",
                    parent=self.dialog
                )

    def open_buy_link(self):
        """Open WhatsApp chat link to buy the tool"""
        # Replace with your WhatsApp number
        whatsapp_number = "+*************"  # e.g., "**********"
        whatsapp_message = "Hi! I'm interested in buying 1ClickVideo."

        # Create WhatsApp link
        whatsapp_link = f"https://wa.me/{whatsapp_number}?text={whatsapp_message}"
        webbrowser.open(whatsapp_link)

    def on_email_focus_in(self, event):
        """Handle email field focus in - implement enhanced floating label effect"""
        self.email_focused = True

        # Clear placeholder if it's showing
        if self.email_entry.get() == self.email_placeholder:
            self.email_entry.delete(0, tk.END)
            self.email_entry.configure(fg=self.ModernColors.TEXT_PRIMARY)

        # Enhanced label animation with color and style changes
        self.email_label.configure(
            fg=self.ModernColors.PRIMARY,
            font=(self.DEFAULT_FONT, 10, 'bold')
        )

        # Update email indicator
        self.email_indicator.configure(
            text="Enter your registered email address",
            fg=self.ModernColors.TEXT_MUTED
        )

    def on_email_focus_out(self, event):
        """Handle email field focus out - restore placeholder if empty"""
        self.email_focused = False

        # If field is empty, restore placeholder
        if not self.email_entry.get():
            self.email_entry.insert(0, self.email_placeholder)
            self.email_entry.configure(fg=self.ModernColors.TEXT_MUTED)
            self.email_indicator.configure(text="")

        # Restore label if field is empty
        if self.email_entry.get() == self.email_placeholder:
            self.email_label.configure(
                fg=self.ModernColors.TEXT_SECONDARY,
                font=(self.DEFAULT_FONT, 10)
            )

    def on_email_change(self, event):
        """Handle email field content changes with enhanced validation feedback"""
        email = self.email_entry.get().strip()

        # Handle placeholder text
        if email == self.email_placeholder:
            email = ""

        # If user is typing and field is not empty
        if self.email_focused and email:
            # Enhanced email format validation
            if '@' in email and '.' in email.split('@')[1] and len(email) > 5:
                self.email_indicator.configure(
                    text="✓ Valid email format",
                    fg=self.ModernColors.SUCCESS
                )
                # Update main status for complete form
                self.status_label.configure(
                    text="✅ Ready to login - Press Enter or click Login button",
                    fg=self.ModernColors.SUCCESS
                )
            elif len(email) > 0:
                self.email_indicator.configure(
                    text="⚠ Please enter a valid email address",
                    fg=self.ModernColors.WARNING
                )
                self.status_label.configure(
                    text="✏️ Please complete your email address",
                    fg=self.ModernColors.TEXT_SECONDARY
                )
            else:
                self.email_indicator.configure(text="")
        elif not email:
            self.email_indicator.configure(text="")

        # Update form validation and button states
        self.update_form_validation()



    def auto_verify_on_enter(self):
        """Automatically verify email when Enter is pressed"""
        # Only proceed if email field has content and is not showing placeholder
        email = self.email_entry.get().strip()
        if email and email != self.email_placeholder:
            # Show verification in progress
            self.show_verification_progress()
            # Verify after a short delay to show animation
            self.dialog.after(800, lambda: self.verify_login_with_email(email))

    def show_verification_progress(self):
        """Show enhanced verification progress animation"""
        # Update status with modern styling
        self.status_label.configure(
            text="🔄 Verifying your credentials...",
            fg=self.ModernColors.INFO
        )

        # Show and animate progress bar with smooth animation
        self.progress_bar.pack(fill=tk.X, side=tk.LEFT)

        # Enhanced progress bar animation with smooth transitions
        def animate_progress(width=0):
            if width <= 100:
                # Calculate actual pixel width based on percentage
                try:
                    frame_width = self.progress_frame.winfo_width()
                    if frame_width > 1:  # Ensure frame is properly sized
                        pixel_width = int(frame_width * (width / 100))
                        self.progress_bar.configure(width=max(1, pixel_width))

                    # Smooth animation with variable speed
                    if width < 30:
                        increment = 3  # Fast start
                    elif width < 70:
                        increment = 2  # Medium speed
                    else:
                        increment = 1  # Slow finish for dramatic effect

                    # Continue animation
                    self.dialog.after(15, lambda: animate_progress(width + increment))
                except tk.TclError:
                    # Handle case where dialog is destroyed during animation
                    pass

        # Start animation after a brief delay to ensure proper sizing
        self.dialog.after(100, animate_progress)

    def open_whatsapp_contact(self):
        """Open WhatsApp chat with developer for license purchase"""
        import webbrowser
        import urllib.parse

        # Use the actual WhatsApp number from class attribute
        whatsapp_number = self.whatsapp_number

        # Pre-filled message for license inquiry
        message = """Hi MD Galib Hasan! 👋

I'm interested in purchasing a license for 1ClickVideo.

Please provide me with:
• License pricing information
• Payment methods available
• Account activation process

Looking forward to using this amazing AI video creation tool!

Best regards"""

        # Create WhatsApp URL with encoded message
        encoded_message = urllib.parse.quote(message)
        whatsapp_url = f"https://wa.me/{whatsapp_number}?text={encoded_message}"

        try:
            webbrowser.open(whatsapp_url)
        except Exception:
            # Fallback - copy to clipboard
            try:
                self.dialog.clipboard_clear()
                self.dialog.clipboard_append(whatsapp_number)
                messagebox.showinfo(
                    "Contact Information",
                    f"WhatsApp number {whatsapp_number} copied to clipboard.",
                    parent=self.dialog
                )
            except:
                pass

    def show_purchase_info(self):
        """Legacy method - redirect to WhatsApp contact"""
        self.open_whatsapp_contact()

    def update_form_validation(self):
        """Update form validation state and button visibility/state"""
        # Get current field values
        email = self.email_entry.get().strip()

        # Handle placeholder text
        if email == self.email_placeholder:
            email = ""

        # Check if form is valid (email has content and email format is valid)
        email_valid = email and '@' in email and '.' in email.split('@')[1]

        self.form_is_valid = email_valid

        # Update login button state
        if hasattr(self, 'login_btn'):
            if self.form_is_valid:
                self.login_btn.configure(state=tk.NORMAL)
            else:
                self.login_btn.configure(state=tk.DISABLED)

        # Check email registration status if email is valid
        if email_valid and email != getattr(self, '_last_checked_email', ''):
            self._last_checked_email = email
            # Check email registration in background
            self.dialog.after(500, lambda: self.check_email_registration(email))

    def check_email_registration(self, email):
        """Check if email is registered and update UI accordingly"""
        if not email or self.sheet is None:
            return

        try:
            # Get all emails from the spreadsheet
            all_emails = self.sheet.col_values(1)[1:]  # Skip header row

            if email.lower() in [e.lower() for e in all_emails]:
                # Email is registered
                self.email_is_registered = True
                self.purchase_btn.pack_forget()  # Hide purchase button
                self.status_label.configure(
                    text="✅ Email verified! Click Login to continue.",
                    fg=self.ModernColors.SUCCESS
                )
            else:
                # Email is not registered
                self.email_is_registered = False
                self.purchase_btn.pack(side=tk.LEFT)  # Show purchase button
                self.status_label.configure(
                    text="❌ Email not registered. Click 'Purchase License' to get access.",
                    fg=self.ModernColors.WARNING
                )
        except Exception as e:
            print(f"Error checking email registration: {str(e)}")
            # On error, assume email might be registered
            self.email_is_registered = None
            self.purchase_btn.pack_forget()

    def handle_login_button(self):
        """Handle login button click with enhanced validation"""
        email = self.email_entry.get().strip()

        # Handle placeholder text
        if email == self.email_placeholder:
            email = ""

        # Validate form
        if not email:
            self.status_label.configure(
                text="❌ Please enter your email address",
                fg=self.ModernColors.ERROR
            )
            self.email_entry.focus_set()
            return

        # Show verification in progress
        self.show_verification_progress()

        # Verify credentials after a short delay to show animation
        self.dialog.after(800, lambda: self.verify_login_with_email(email))

    def handle_purchase_button(self):
        """Handle purchase button click"""
        self.open_whatsapp_contact()

    def verify_login_with_email(self, email):
        """Verify login with email only"""
        if self.sheet is None:
            # Try to reconnect once
            self.init_google_sheets()

            if self.sheet is None:
                self.status_label.configure(
                    text="🌐 Unable to connect to server. Check your internet connection.",
                    fg=self.ModernColors.ERROR
                )
                # Hide progress bar
                if hasattr(self, 'progress_bar'):
                    self.progress_bar.pack_forget()
                return

        try:
            # Get all emails from the spreadsheet
            all_emails = self.sheet.col_values(1)[1:]  # Skip header row

            if email.lower() in [e.lower() for e in all_emails]:
                # Valid email found - show success
                self.status_label.configure(
                    text="✅ Login successful! Welcome back.",
                    fg=self.ModernColors.SUCCESS
                )
                # Hide progress bar
                if hasattr(self, 'progress_bar'):
                    self.progress_bar.pack_forget()

                # Valid login
                self.result = True

                # Save credentials for future logins
                self.credentials_manager.save_credentials(email)

                # Small delay to show success message
                self.dialog.after(1000, self.dialog.destroy)
            else:
                # Email not found in the spreadsheet
                self.status_label.configure(
                    text="❌ Email not registered. Please check your email or purchase a license.",
                    fg=self.ModernColors.ERROR
                )
                # Hide progress bar
                if hasattr(self, 'progress_bar'):
                    self.progress_bar.pack_forget()

                # Show purchase button for unregistered email
                self.email_is_registered = False
                self.purchase_btn.pack(side=tk.LEFT)

        except Exception as e:
            print(f"Error verifying login: {str(e)}")
            self.status_label.configure(
                text="⚠️ Error verifying credentials. Please try again.",
                fg=self.ModernColors.ERROR
            )
            # Hide progress bar
            if hasattr(self, 'progress_bar'):
                self.progress_bar.pack_forget()

def show_login_dialog(root):
    """Show the login dialog and return True if login successful"""
    # Check if user is already logged in
    credentials_manager = CredentialsManager()
    if credentials_manager.is_logged_in():
        # User is already logged in, no need to show dialog
        return True

    # User is not logged in, show the login dialog
    dialog = LoginDialog(root)
    root.wait_window(dialog.dialog)
    return dialog.result

def logout_user():
    """Log out the current user by clearing saved credentials"""
    credentials_manager = CredentialsManager()
    return credentials_manager.clear_credentials()

def get_current_user_email():
    """Get the email of the currently logged in user"""
    credentials_manager = CredentialsManager()
    return credentials_manager.get_email()