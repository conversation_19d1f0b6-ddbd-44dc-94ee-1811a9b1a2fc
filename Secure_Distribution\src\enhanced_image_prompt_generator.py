"""
Enhanced Image Prompt Generator

This module provides advanced image prompt generation capabilities with improved:
- Scene context understanding
- Character detection (gender, age, emotions, roles)
- Visual element extraction
- Style adaptation
- Mood and tone recognition

It analyzes script text to generate highly relevant, detailed image prompts
that accurately represent the scene content.
"""

import os
import re
import string
from typing import Dict, List, Any, Optional, Tuple, Set

# Common English stopwords to filter out
COMMON_STOPWORDS = {
    "a", "an", "the", "and", "or", "but", "if", "because", "as", "what", "when",
    "where", "how", "who", "which", "this", "that", "these", "those", "then",
    "just", "so", "than", "such", "both", "through", "about", "for", "is", "of",
    "while", "during", "to", "from", "in", "out", "on", "off", "over", "under",
    "again", "further", "then", "once", "here", "there", "all", "any", "both",
    "each", "few", "more", "most", "other", "some", "such", "no", "nor", "not",
    "only", "own", "same", "too", "very", "can", "will", "should", "now", "i",
    "me", "my", "myself", "we", "our", "ours", "ourselves", "you", "your", "yours",
    "yourself", "yourselves", "he", "him", "his", "himself", "she", "her", "hers",
    "herself", "it", "its", "itself", "they", "them", "their", "theirs", "themselves",
    "am", "are", "was", "were", "be", "been", "being", "have", "has", "had", "having",
    "do", "does", "did", "doing", "would", "should", "could", "ought", "i'm", "you're",
    "he's", "she's", "it's", "we're", "they're", "i've", "you've", "we've", "they've",
    "i'd", "you'd", "he'd", "she'd", "we'd", "they'd", "i'll", "you'll", "he'll", "she'll",
    "we'll", "they'll", "isn't", "aren't", "wasn't", "weren't", "hasn't", "haven't", "hadn't",
    "doesn't", "don't", "didn't", "won't", "wouldn't", "shan't", "shouldn't", "can't", "cannot",
    "couldn't", "mustn't", "let's", "that's", "who's", "what's", "here's", "there's", "when's",
    "why's", "how's"
}

# Character-related patterns and keywords
MALE_INDICATORS = {
    "man", "boy", "gentleman", "male", "father", "dad", "son", "brother", "uncle",
    "grandfather", "grandpa", "husband", "boyfriend", "king", "prince", "he", "him", "his",
    "himself", "mr", "sir", "guy", "dude", "fellow", "lad", "gent"
}

FEMALE_INDICATORS = {
    "woman", "girl", "lady", "female", "mother", "mom", "daughter", "sister", "aunt",
    "grandmother", "grandma", "wife", "girlfriend", "queen", "princess", "she", "her", "hers",
    "herself", "mrs", "miss", "ms", "madam", "ma'am", "gal"
}

AGE_INDICATORS = {
    "infant": ["infant", "baby", "newborn", "toddler"],
    "child": ["child", "kid", "young", "little", "small", "elementary", "boy", "girl"],
    "teenager": ["teen", "teenage", "adolescent", "youth", "young adult", "high school"],
    "adult": ["adult", "grown", "man", "woman", "middle-aged", "professional", "worker"],
    "elderly": ["elderly", "old", "senior", "aged", "retired", "grandfather", "grandmother", "grandpa", "grandma"]
}

EMOTION_INDICATORS = {
    "happy": ["happy", "joyful", "smiling", "cheerful", "delighted", "ecstatic", "elated", "pleased", "thrilled", "content", "satisfied"],
    "sad": ["sad", "unhappy", "sorrowful", "depressed", "gloomy", "miserable", "downcast", "tearful", "crying", "weeping", "melancholic"],
    "angry": ["angry", "furious", "enraged", "irate", "annoyed", "irritated", "mad", "outraged", "hostile", "heated", "fuming"],
    "afraid": ["afraid", "scared", "frightened", "terrified", "fearful", "anxious", "nervous", "worried", "panicked", "alarmed", "horrified"],
    "surprised": ["surprised", "shocked", "astonished", "amazed", "stunned", "startled", "dumbfounded", "bewildered", "awestruck", "taken aback"],
    "disgusted": ["disgusted", "revolted", "repulsed", "nauseated", "appalled", "repelled", "sickened", "offended", "averse"],
    "calm": ["calm", "peaceful", "serene", "tranquil", "relaxed", "composed", "collected", "placid", "quiet", "still", "untroubled"],
    "excited": ["excited", "enthusiastic", "eager", "animated", "energetic", "lively", "spirited", "thrilled", "exhilarated", "stimulated"],
    "thoughtful": ["thoughtful", "pensive", "contemplative", "reflective", "meditative", "introspective", "philosophical", "deep in thought"],
    "determined": ["determined", "resolute", "steadfast", "firm", "committed", "dedicated", "persistent", "unwavering", "focused", "driven"],
    "confused": ["confused", "puzzled", "perplexed", "baffled", "bewildered", "disoriented", "muddled", "uncertain", "unsure", "lost"],
    "tired": ["tired", "exhausted", "weary", "fatigued", "drained", "sleepy", "drowsy", "lethargic", "spent", "worn out"],
    "confident": ["confident", "self-assured", "certain", "sure", "poised", "bold", "assertive", "self-reliant", "secure", "positive"],
    "loving": ["loving", "affectionate", "caring", "tender", "warm", "adoring", "fond", "devoted", "compassionate", "doting"]
}

ROLE_INDICATORS = {
    "professional": ["doctor", "lawyer", "teacher", "professor", "engineer", "scientist", "nurse", "chef", "artist", "musician",
                    "writer", "journalist", "police", "officer", "firefighter", "soldier", "pilot", "astronaut", "architect",
                    "designer", "programmer", "developer", "manager", "executive", "ceo", "entrepreneur", "business"],
    "family": ["parent", "mother", "father", "son", "daughter", "brother", "sister", "uncle", "aunt", "cousin", "grandparent",
              "grandmother", "grandfather", "husband", "wife", "spouse", "family", "relative", "in-law"],
    "social": ["friend", "neighbor", "colleague", "classmate", "roommate", "teammate", "partner", "companion", "acquaintance",
              "associate", "ally", "buddy", "pal", "peer", "comrade"],
    "authority": ["leader", "boss", "supervisor", "manager", "director", "chief", "head", "president", "principal", "commander",
                 "captain", "general", "officer", "official", "authority", "ruler", "monarch", "king", "queen"],
    "student": ["student", "pupil", "learner", "scholar", "apprentice", "trainee", "intern", "graduate", "undergraduate",
               "freshman", "sophomore", "junior", "senior", "phd", "candidate", "researcher"]
}

def detect_characters(text: str) -> List[Dict[str, Any]]:
    """
    Detect characters in the text along with their attributes (gender, age, emotion, role).

    Args:
        text: The text to analyze

    Returns:
        List of dictionaries containing character information
    """
    # Normalize text
    text_lower = text.lower()

    # Find potential character mentions
    characters = []

    # Look for explicit character mentions with descriptors
    character_patterns = [
        # Patterns for detecting characters with descriptors
        r'(?:a|an|the)\s+([a-z]+(?:\s+[a-z]+)?)\s+(?:man|woman|boy|girl|person|guy|lady|gentleman|individual)',
        r'(?:a|an|the)\s+([a-z]+)\s+([a-z]+)\s+(?:man|woman|boy|girl|person|guy|lady|gentleman|individual)',
        r'(?:a|an|the)\s+([a-z]+)(?:-|\s+)([a-z]+)\s+(?:man|woman|boy|girl|person|guy|lady|gentleman|individual)',
        r'(?:a|an|the)\s+([a-z]+)\s+(?:man|woman|boy|girl|person|guy|lady|gentleman|individual)',
        r'(?:a|an|the)\s+(?:man|woman|boy|girl|person|guy|lady|gentleman|individual)\s+(?:with|wearing|who|that)\s+([a-z]+(?:\s+[a-z]+)?)',
        r'(?:a|an|the)\s+(?:young|old|middle-aged|elderly)\s+(?:man|woman|boy|girl|person|guy|lady|gentleman|individual)',
        r'(?:a|an|the)\s+(?:[a-z]+)\s+(?:man|woman|boy|girl|person|guy|lady|gentleman|individual)',
    ]

    # Extract character mentions
    character_mentions = []
    for pattern in character_patterns:
        matches = re.findall(pattern, text_lower)
        for match in matches:
            if isinstance(match, tuple):
                # Join tuple elements if the match is a tuple
                character_mentions.append(" ".join([m for m in match if m]))
            else:
                character_mentions.append(match)

    # If no specific character mentions found, check for general character indicators
    if not character_mentions:
        # Check for pronouns and other character indicators
        if any(word in text_lower for word in MALE_INDICATORS):
            character_mentions.append("man")
        if any(word in text_lower for word in FEMALE_INDICATORS):
            character_mentions.append("woman")

    # Process each character mention
    for mention in character_mentions:
        character = {
            "description": mention,
            "gender": None,
            "age": None,
            "emotions": [],
            "roles": []
        }

        # Detect gender
        if any(indicator in text_lower for indicator in MALE_INDICATORS):
            character["gender"] = "male"
        elif any(indicator in text_lower for indicator in FEMALE_INDICATORS):
            character["gender"] = "female"

        # Detect age
        for age_group, indicators in AGE_INDICATORS.items():
            if any(indicator in text_lower for indicator in indicators):
                character["age"] = age_group
                break

        # Detect emotions
        for emotion, indicators in EMOTION_INDICATORS.items():
            if any(indicator in text_lower for indicator in indicators):
                character["emotions"].append(emotion)

        # Detect roles
        for role, indicators in ROLE_INDICATORS.items():
            if any(indicator in text_lower for indicator in indicators):
                character["roles"].append(role)

        characters.append(character)

    return characters

def extract_scene_elements(text: str) -> Dict[str, List[str]]:
    """
    Extract detailed scene elements from text.

    Args:
        text: The text to analyze

    Returns:
        Dictionary of scene elements by category
    """
    # Initialize scene elements dictionary
    scene_elements = {
        "setting": [],
        "location": [],
        "time_of_day": [],
        "weather": [],
        "objects": [],
        "actions": [],
        "mood": [],
        "lighting": [],
        "colors": [],
        "composition": []
    }

    # Setting and location patterns
    setting_patterns = [
        r'(?:in|at|on) (?:a|an|the) ([^,.]+)',
        r'setting:? ([^,.]+)',
        r'environment:? ([^,.]+)',
        r'location:? ([^,.]+)',
        r'scene (?:shows|depicts|features) ([^,.]+)',
        r'backdrop of ([^,.]+)',
        r'(?:inside|outside|outdoors|indoors) (?:a|an|the) ([^,.]+)',
    ]

    # Time of day patterns
    time_patterns = [
        r'(?:during|at) (dawn|daybreak|sunrise|morning|noon|afternoon|evening|dusk|sunset|twilight|night|midnight)',
        r'(dawn|daybreak|sunrise|morning|noon|afternoon|evening|dusk|sunset|twilight|night|midnight) (?:sky|scene|light|time)',
        r'(?:early|late) (morning|afternoon|evening|night)',
        r'time of day (?:is|appears) ([^,.]+)',
    ]

    # Weather patterns
    weather_patterns = [
        r'(sunny|cloudy|rainy|stormy|snowy|foggy|misty|clear|overcast) (?:day|weather|conditions)',
        r'weather (?:is|appears) ([^,.]+)',
        r'(?:rain|snow|fog|mist|clouds|sunshine) ([^,.]+)',
        r'(?:in|during) (?:a|the) (rain|snow|storm|fog|mist)',
    ]

    # Object patterns
    object_patterns = [
        r'(?:a|an|the) ([^,.]+) (?:sits|stands|lies|placed|positioned|located)',
        r'(?:with|containing|featuring) (?:a|an|the) ([^,.]+)',
        r'(?:surrounded by|filled with|decorated with) ([^,.]+)',
    ]

    # Action patterns
    action_patterns = [
        r'([a-z]+ing) (?:the|a|an|across|through|over|under)',
        r'(?:is|are) ([a-z]+ing)',
        r'(?:person|man|woman|child|individual|figure|character|subject|people|group) (?:is|are) ([^,.]+?ing)',
    ]

    # Mood patterns
    mood_patterns = [
        r'mood (?:is|appears) ([^,.]+)',
        r'atmosphere (?:is|appears) ([^,.]+)',
        r'feeling of ([^,.]+)',
        r'sense of ([^,.]+)',
        r'evoking ([^,.]+)',
        r'creating (?:a|an) ([^,.]+) (?:mood|atmosphere|feeling|sense)',
    ]

    # Lighting patterns
    lighting_patterns = [
        r'lighting (?:is|appears) ([^,.]+)',
        r'lit by ([^,.]+)',
        r'light(?:ing)? (?:is|appears) ([^,.]+)',
        r'([^,.]+) lighting',
        r'([^,.]+) light',
        r'illuminated by ([^,.]+)',
    ]

    # Color patterns
    color_patterns = [
        r'colors? (?:is|are|of) ([^,.]+)',
        r'([^,.]+) colors?',
        r'color palette (?:is|of) ([^,.]+)',
        r'hues of ([^,.]+)',
        r'tones of ([^,.]+)',
        r'palette of ([^,.]+)',
    ]

    # Composition patterns
    composition_patterns = [
        r'composition (?:is|shows) ([^,.]+)',
        r'framed ([^,.]+)',
        r'arranged ([^,.]+)',
        r'positioned ([^,.]+)',
        r'layout (?:is|shows) ([^,.]+)',
        r'(?:foreground|background|middle ground) ([^,.]+)',
        r'(?:close-up|medium shot|wide shot|overhead|aerial) ([^,.]+)',
    ]

    # Extract elements using patterns
    pattern_categories = [
        ("setting", setting_patterns),
        ("time_of_day", time_patterns),
        ("weather", weather_patterns),
        ("objects", object_patterns),
        ("actions", action_patterns),
        ("mood", mood_patterns),
        ("lighting", lighting_patterns),
        ("colors", color_patterns),
        ("composition", composition_patterns),
    ]

    for category, patterns in pattern_categories:
        for pattern in patterns:
            matches = re.findall(pattern, text.lower(), re.IGNORECASE)
            if matches:
                for match in matches:
                    if isinstance(match, tuple):
                        scene_elements[category].append(match[-1].strip())
                    else:
                        scene_elements[category].append(match.strip())

    # If we couldn't extract specific elements, use key sentences
    if all(len(elements) == 0 for elements in scene_elements.values()):
        sentences = re.split(r'[.!?]', text)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 10]

        # Prioritize sentences with visual words
        visual_words = ['shows', 'displays', 'depicts', 'features', 'contains', 'with', 'scene', 'image', 'camera', 'shot', 'angle', 'lighting', 'color']
        visual_sentences = [s for s in sentences if any(word in s.lower() for word in visual_words)]

        if visual_sentences:
            scene_elements["setting"] = visual_sentences[:2]
        else:
            scene_elements["setting"] = sentences[:2]

    return scene_elements

def analyze_scene_context(text: str) -> Dict[str, Any]:
    """
    Perform comprehensive scene context analysis.

    Args:
        text: The text to analyze

    Returns:
        Dictionary with scene context information
    """
    # Extract scene elements
    scene_elements = extract_scene_elements(text)

    # Detect characters
    characters = detect_characters(text)

    # Analyze overall scene type
    scene_type = determine_scene_type(text)

    # Combine all information
    scene_context = {
        "elements": scene_elements,
        "characters": characters,
        "scene_type": scene_type
    }

    return scene_context

def determine_scene_type(text: str) -> str:
    """
    Determine the type of scene based on text content.

    Args:
        text: The text to analyze

    Returns:
        Scene type classification
    """
    text_lower = text.lower()

    # Scene type indicators
    scene_types = {
        "action": ["running", "jumping", "fighting", "chasing", "racing", "battle", "explosion", "combat", "attack", "defend", "escape"],
        "emotional": ["crying", "laughing", "smiling", "weeping", "embracing", "hugging", "kissing", "mourning", "celebrating", "emotional"],
        "dramatic": ["confrontation", "revelation", "discovery", "shocking", "surprising", "dramatic", "intense", "climactic", "pivotal"],
        "peaceful": ["calm", "serene", "peaceful", "quiet", "still", "tranquil", "relaxing", "meditative", "resting", "sleeping"],
        "mysterious": ["mysterious", "enigmatic", "puzzling", "cryptic", "secretive", "hidden", "obscured", "shadowy", "unknown"],
        "fantasy": ["magical", "enchanted", "mystical", "supernatural", "fantastical", "otherworldly", "fairy", "dragon", "wizard", "spell"],
        "sci-fi": ["futuristic", "technological", "scientific", "space", "alien", "robot", "cyborg", "spacecraft", "laboratory", "experiment"],
        "horror": ["scary", "frightening", "terrifying", "horror", "monster", "ghost", "haunted", "creepy", "eerie", "sinister", "dark"],
        "romantic": ["romantic", "love", "passion", "intimate", "tender", "affectionate", "couple", "relationship", "date", "wedding"],
        "comedy": ["funny", "humorous", "comedic", "laughing", "joke", "silly", "amusing", "entertaining", "lighthearted", "playful"],
        "nature": ["landscape", "mountain", "ocean", "forest", "river", "lake", "beach", "wilderness", "natural", "outdoors", "scenic"],
        "urban": ["city", "street", "building", "skyscraper", "urban", "downtown", "metropolitan", "skyline", "traffic", "crowded"],
        "historical": ["historical", "ancient", "medieval", "vintage", "retro", "old", "traditional", "classic", "period", "era", "century"]
    }

    # Count occurrences of each scene type indicator
    type_scores = {scene_type: 0 for scene_type in scene_types}

    for scene_type, indicators in scene_types.items():
        for indicator in indicators:
            if indicator in text_lower:
                type_scores[scene_type] += 1

    # Get the scene type with the highest score
    if any(score > 0 for score in type_scores.values()):
        max_score = max(type_scores.values())
        top_types = [scene_type for scene_type, score in type_scores.items() if score == max_score]
        return top_types[0]  # Return the first highest-scoring type
    else:
        return "general"  # Default scene type

def map_story_type(ui_story_type: str) -> str:
    """
    Map UI story type to internal story type used by the image generator.

    Args:
        ui_story_type: Story type from the UI

    Returns:
        Corresponding internal story type
    """
    # Convert to lowercase for case-insensitive matching
    ui_type_lower = ui_story_type.lower()

    # Direct mappings with expanded list
    direct_map = {
        "scary": "scary",
        "mystery": "mystery",
        "bedtime": "bedtime",
        "love": "love",
        "philosophy": "philosophy",
        "motivational": "motivational",
        "pet tips": "pet_tips",  # Changed to underscore for consistency
        "pet": "pet_tips",
        "animal": "pet_tips",
        "dog": "pet_tips",
        "cat": "pet_tips",
        "bird": "pet_tips",
        "fish": "pet_tips",
        "health": "health",
        "fitness": "fitness",
        "food": "food",
        "nutrition": "nutrition",
        "diet": "nutrition",
        "travel": "travel",
        "education": "education",
        "technology": "technology",
        "tech": "technology",
        "business": "business",
        "finance": "finance",
        "money": "finance",
        "science": "science",
        "history": "history",
        "art": "art",
        "music": "music",
        "sports": "sports",
        "gaming": "gaming",
        "fashion": "fashion",
        "beauty": "beauty",
        "lifestyle": "lifestyle",
        "parenting": "parenting",
        "relationships": "relationships",
        "self-improvement": "self_improvement",
        "spirituality": "spirituality",
        "religion": "religion",
        "politics": "politics",
        "news": "news",
        "entertainment": "entertainment",
        "celebrity": "celebrity",
        "humor": "humor",
        "comedy": "comedy",
        "horror": "horror",
        "thriller": "thriller",
        "action": "action",
        "adventure": "adventure",
        "fantasy": "fantasy",
        "sci-fi": "sci_fi",
        "scifi": "sci_fi",
        "science fiction": "sci_fi",
        "romance": "romance",
        "drama": "drama",
        "documentary": "documentary",
        "animation": "animation",
        "family": "family",
        "children": "children",
        "kids": "children",
        "medical": "health",
        "medicine": "health",
        "wellness": "health",
        "mental health": "psychology",
        "psychology": "psychology",
        "mental": "psychology",
        "mind": "psychology",
        "brain": "psychology",
        "behavior": "psychology"
    }

    # If there's a direct match, return it
    if ui_type_lower in direct_map:
        return direct_map[ui_type_lower]

    # Category-based mappings for more complex or partial matches
    if any(keyword in ui_type_lower for keyword in ["psychology", "mental", "mind", "brain", "behavior", "cognitive", "emotional"]):
        return "psychology"

    if any(keyword in ui_type_lower for keyword in ["philosophy", "philosophical", "ethics", "moral", "existence", "meaning", "purpose"]):
        return "philosophy"

    if any(keyword in ui_type_lower for keyword in ["motivational", "motivation", "inspire", "success", "achievement", "goals", "ambition", "dreams", "personal growth"]):
        return "motivational"

    if any(keyword in ui_type_lower for keyword in ["love", "relationship", "dating", "romance", "couple", "marriage", "partner", "affection"]):
        return "love"

    if any(keyword in ui_type_lower for keyword in ["scary", "horror", "fear", "terror", "creepy", "spooky", "crime", "conspiracy", "thriller", "suspense"]):
        return "scary"

    if any(keyword in ui_type_lower for keyword in ["mystery", "detective", "puzzle", "solve", "clue", "urban legends", "enigma", "riddle"]):
        return "mystery"

    if any(keyword in ui_type_lower for keyword in ["bedtime", "sleep", "night", "rest", "relax", "calm", "soothing", "peaceful"]):
        return "bedtime"

    if any(keyword in ui_type_lower for keyword in ["pet", "animal", "dog", "cat", "bird", "fish", "hamster", "rabbit", "reptile", "amphibian", "veterinary", "pet care"]):
        return "pet_tips"

    if any(keyword in ui_type_lower for keyword in ["health", "wellness", "nutrition", "diet", "medical", "medicine", "doctor", "hospital", "clinic", "symptom", "disease", "condition", "treatment", "therapy", "healing", "healthcare"]):
        return "health"

    if any(keyword in ui_type_lower for keyword in ["fitness", "exercise", "workout", "training", "gym", "sport", "athletic", "physical activity"]):
        return "fitness"

    if any(keyword in ui_type_lower for keyword in ["food", "cooking", "recipe", "cuisine", "culinary", "gastronomy", "meal", "dish", "ingredient"]):
        return "food"

    if any(keyword in ui_type_lower for keyword in ["tech", "technology", "digital", "computer", "software", "hardware", "internet", "ai", "artificial intelligence", "gadget", "device"]):
        return "technology"

    # Default to "general" which will use a combination of psychology and motivational concepts
    return "general"

def create_character_description(characters: List[Dict[str, Any]]) -> str:
    """
    Create a detailed character description from character data.

    Args:
        characters: List of character dictionaries

    Returns:
        Character description string
    """
    if not characters:
        return ""

    # Deduplicate characters by description to avoid repetition
    unique_characters = {}
    for character in characters:
        desc = character["description"]
        if desc not in unique_characters:
            unique_characters[desc] = character

    character_descriptions = []

    for description, character in unique_characters.items():
        parts = []

        # Add age if available
        if character["age"]:
            parts.append(character["age"])

        # Add gender if available
        if character["gender"]:
            parts.append(character["gender"])

        # Add emotions if available (limit to one primary emotion)
        if character["emotions"]:
            parts.append(character["emotions"][0])

        # Add roles if available (limit to one primary role)
        if character["roles"]:
            parts.append(character["roles"][0])

        # Combine all parts with the description
        if parts:
            # Clean up the description to avoid redundancy
            clean_desc = description
            for part in parts:
                if part in clean_desc:
                    clean_desc = clean_desc.replace(part, "").strip()

            if clean_desc:
                character_descriptions.append(f"a {' '.join(parts)} {clean_desc}")
            else:
                character_descriptions.append(f"a {' '.join(parts)}")
        else:
            character_descriptions.append(f"a {description}")

    # Limit to at most 2 characters to avoid overcrowding
    if len(character_descriptions) > 2:
        character_descriptions = character_descriptions[:2]

    # Combine all character descriptions
    if len(character_descriptions) == 1:
        return character_descriptions[0]
    elif len(character_descriptions) == 2:
        return f"{character_descriptions[0]} and {character_descriptions[1]}"
    else:
        return ", ".join(character_descriptions[:-1]) + f", and {character_descriptions[-1]}"

def create_setting_description(scene_elements: Dict[str, List[str]]) -> str:
    """
    Create a detailed setting description from scene elements.

    Args:
        scene_elements: Dictionary of scene elements

    Returns:
        Setting description string
    """
    setting_parts = []

    # Add location/setting
    if scene_elements["setting"]:
        setting_parts.append(scene_elements["setting"][0])
    elif scene_elements["location"]:
        setting_parts.append(scene_elements["location"][0])

    # Add time of day
    if scene_elements["time_of_day"]:
        setting_parts.append(f"during {scene_elements['time_of_day'][0]}")

    # Add weather
    if scene_elements["weather"]:
        setting_parts.append(f"with {scene_elements['weather'][0]} weather")

    # Add lighting
    if scene_elements["lighting"]:
        setting_parts.append(f"with {scene_elements['lighting'][0]} lighting")

    # Add mood/atmosphere
    if scene_elements["mood"]:
        setting_parts.append(f"creating a {scene_elements['mood'][0]} atmosphere")

    # Add colors
    if scene_elements["colors"]:
        setting_parts.append(f"with {scene_elements['colors'][0]} color palette")

    # Combine all parts
    if setting_parts:
        return " ".join(setting_parts)
    else:
        return ""

def create_action_description(scene_elements: Dict[str, List[str]]) -> str:
    """
    Create an action description from scene elements.

    Args:
        scene_elements: Dictionary of scene elements

    Returns:
        Action description string
    """
    if scene_elements["actions"]:
        return scene_elements["actions"][0]
    else:
        return ""

def create_composition_description(scene_elements: Dict[str, List[str]]) -> str:
    """
    Create a composition description from scene elements.

    Args:
        scene_elements: Dictionary of scene elements

    Returns:
        Composition description string
    """
    if scene_elements["composition"]:
        return f"with {scene_elements['composition'][0]} composition"
    else:
        return ""

def generate_image_prompt(text: str, story_type: str = "general", image_style: str = "cinematic") -> str:
    """
    Generate a detailed image prompt from text.

    Args:
        text: The text to analyze
        story_type: Type of story
        image_style: Style for the image

    Returns:
        Detailed image prompt
    """
    # Normalize image style for consistent handling
    image_style_normalized = image_style.lower().replace(" ", "").replace("-", "")

    # Print debug information
    print(f"Generating image prompt with style: {image_style} (normalized: {image_style_normalized})")

    # Analyze the scene context
    scene_context = analyze_scene_context(text)

    # Create character description
    character_description = create_character_description(scene_context["characters"])

    # Create setting description
    setting_description = create_setting_description(scene_context["elements"])

    # Create action description
    action_description = create_action_description(scene_context["elements"])

    # Create composition description
    composition_description = create_composition_description(scene_context["elements"])

    # Combine all descriptions
    scene_description_parts = []

    if setting_description:
        scene_description_parts.append(setting_description)

    if character_description:
        if action_description:
            scene_description_parts.append(f"{character_description} {action_description}")
        else:
            scene_description_parts.append(character_description)
    elif action_description:
        scene_description_parts.append(action_description)

    if composition_description:
        scene_description_parts.append(composition_description)

    # If we couldn't extract enough information, use the original text
    if not scene_description_parts:
        # Extract key phrases from the text
        sentences = re.split(r'[.!?]', text)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 10]

        if sentences:
            # Use the first sentence as the main description
            main_sentence = sentences[0]

            # Check if the sentence is too long
            if len(main_sentence.split()) > 20:
                # Truncate to first 20 words
                words = main_sentence.split()
                main_sentence = " ".join(words[:20])

            scene_description = main_sentence
        else:
            scene_description = text
    else:
        scene_description = ", ".join(scene_description_parts)

    # Create the final prompt with style-specific enhancements
    image_style_lower = image_style.lower()
    image_style_normalized = image_style.lower().replace(" ", "").replace("-", "")

    # Print debug information
    print(f"Looking up style template for: {image_style_normalized}")

    # Map of all image styles to their prompt templates using normalized keys
    style_prompts = {
        "photorealistic": f"Highly detailed, photorealistic 8K image of {scene_description}. Hyperrealistic, professional photography, perfect lighting, ultra-detailed, photographic quality.",

        "cinematic": f"Highly detailed, cinematic 8K image of {scene_description}. Professional lighting, masterful composition, dramatic atmosphere, high-quality production, movie still quality.",

        "anime": f"Japanese anime style illustration of {scene_description}. Vibrant colors, clean linework, studio Ghibli inspired, detailed character design, high quality anime aesthetic.",

        "comicbook": f"Professional comic book illustration of {scene_description}. Dynamic poses, bold outlines, vibrant colors, Marvel/DC style, detailed shading and crosshatching, rich textures, action-packed composition.",

        "pixelart": f"Detailed pixel art of {scene_description}. 16-bit style, limited color palette, crisp edges, nostalgic retro gaming aesthetic, charming design style.",

        "pixarart": f"3D animated Pixar style rendering of {scene_description}. Characteristic Pixar aesthetic, expressive characters, soft lighting, vibrant colors, playful design, high-quality 3D animation style.",

        "digitalart": f"Professional digital art illustration of {scene_description}. High resolution, vivid colors, detailed textures, fantasy art style, impressive light effects, cohesive composition.",

        "oilpainting": f"Fine art oil painting of {scene_description}. Rich textures, visible brushstrokes, depth through glazing, classical composition, warm color palette, museum-quality, reminiscent of old masters.",

        "watercolor": f"Delicate watercolor painting of {scene_description}. Soft color washes, flowing transitions, subtle bleeding edges, translucent layers, loose brushwork, organic feel, on textured paper.",

        "darkaesthetic": f"Dark moody atmosphere of {scene_description}. Deep shadows and high contrast, low-key lighting, limited dark color palette, gothic elements, mysterious atmosphere, dramatic cinematic style.",

        "neoncyberpunk": f"Futuristic cyberpunk scene of {scene_description}. Bright neon lights in magenta and cyan, rainy reflective streets, advanced technology, dystopian urban setting, Blade Runner inspired, holographic displays, high-contrast lighting.",

        "minimalist": f"Minimalist design of {scene_description}. Simple geometric shapes, limited color palette, negative space, clean composition, essential elements only, modern aesthetic.",

        "filmnoir": f"Black and white film noir style of {scene_description}. High contrast, dramatic shadows, moody lighting, 1940s aesthetic, cinematic composition, mysterious atmosphere.",

        "retro80s": f"1980s retro style of {scene_description}. Synthwave aesthetic, neon colors, grid patterns, sunset gradients, VHS quality, nostalgic 80s vibe, retro futurism.",

        "vaporwave": f"Vaporwave aesthetic of {scene_description}. Pastel colors, glitch effects, Roman busts, checkerboard patterns, retro computing elements, 90s internet culture, surreal composition.",

        "cottagecore": f"Cottagecore aesthetic of {scene_description}. Rustic countryside setting, warm natural lighting, wildflowers, vintage elements, cozy atmosphere, soft colors, pastoral romanticism.",

        "hyperrealistic": f"Ultra-detailed hyperrealistic image of {scene_description}. Indistinguishable from photography, extreme textures and details, perfect lighting, flawless rendering, photographic realism.",

        "flatdesign": f"Modern flat design illustration of {scene_description}. Clean vector style, solid colors without gradients, simplified shapes, minimalistic approach, 2D aesthetic, professional graphic design.",

        "3dcartoon": f"3D animated cartoon style of {scene_description}. Pixar/Dreamworks quality, exaggerated proportions, smooth textures, vibrant colors, expressive characters, playful design.",

        "pasteldreamscape": f"Dreamy pastel fantasy landscape of {scene_description}. Soft color palette, ethereal atmosphere, magical elements, hazy lighting, whimsical design, floating objects, surreal and enchanting.",

        "fantasyvibrant": f"Vibrant fantasy illustration of {scene_description}. Rich saturated colors, magical atmosphere, elaborate details, mythical elements, dramatic lighting, epic composition, high-quality digital artwork.",

        "nostalgicfilter": f"Nostalgic photograph with vintage filter of {scene_description}. Warm amber glow, slight grain texture, light leaks, faded colors, 70s/80s photography style, authentic retro photography.",

        "vhsaesthetic": f"VHS style image of {scene_description}. Tracking lines, chromatic aberration, interlaced scan lines, low resolution, bleeding colors, warped edges, 80s/90s camcorder look, authentic analog video aesthetic.",

        "y2k": f"Y2K aesthetic of {scene_description}. Early 2000s design, glossy metallic elements, bright neon colors, bubble shapes, futuristic yet nostalgic, digital artifacts, millennial aesthetic, authentic early digital era vibes.",

        "godanimevine": f"Bold, exaggerated God Anime Vine style of {scene_description}. Dramatic anime aesthetic, intense expressions, vibrant saturated colors, extreme contrast, dynamic action poses, bold outlines, exaggerated features, viral social media aesthetic.",

        "ghibli": f"Studio Ghibli style illustration of {scene_description}. Soft watercolor-like textures, gentle color palette, whimsical characters, detailed natural environments, dreamy atmospheric lighting, hand-drawn animation quality, Miyazaki-inspired artistic sensibility."
    }

    # Special handling for anime and ghibli styles
    if "anime" in image_style_lower:
        print("Anime style detected, using anime template")
        prompt = f"Japanese anime style illustration of {scene_description}. Vibrant colors, clean linework, studio Ghibli inspired, detailed character design, high quality anime aesthetic."
    elif "ghibli" in image_style_lower:
        print("Ghibli style detected, using ghibli template")
        prompt = f"Studio Ghibli style illustration of {scene_description}. Soft watercolor-like textures, gentle color palette, whimsical characters, detailed natural environments, dreamy atmospheric lighting, hand-drawn animation quality, Miyazaki-inspired artistic sensibility."
    else:
        # Get the prompt for the specified style, or use a default if not found
        prompt = style_prompts.get(image_style_normalized)

        if prompt:
            print(f"Found style template for: {image_style_normalized}")
        else:
            print(f"No exact match found for style: {image_style_normalized}, using default template")
            prompt = f"High-quality detailed image of {scene_description}. Professional lighting, detailed composition, vivid colors."

    # Add a reminder for no text
    prompt += " NO TEXT, no words, no writing, no captions, no labels."

    return prompt

def generate_image_prompt_for_custom_script(paragraph: str, story_type: str = "general", image_style: str = "cinematic") -> str:
    """
    Generate an image prompt for a custom script paragraph.

    Args:
        paragraph: The paragraph text
        story_type: Type of story
        image_style: Style for the image

    Returns:
        Image prompt
    """
    return generate_image_prompt(paragraph, story_type, image_style)

def generate_health_topic_prompt(text: str, topic: str, image_style: str = "photorealistic") -> str:
    """
    Generate a specialized image prompt for health-related topics.

    Args:
        text: The text to analyze
        topic: Specific health topic (e.g., "nutrition", "fitness", "medical")
        image_style: Style for the image

    Returns:
        Health-focused image prompt
    """
    # Normalize topic
    topic_lower = topic.lower()

    # Extract key health terms from the text
    health_terms = extract_health_terms(text)

    # Create a base description based on the topic
    if "nutrition" in topic_lower or "food" in topic_lower or "diet" in topic_lower:
        # Check for specific foods
        food_items = extract_food_items(text)
        if food_items:
            base_description = f"Healthy {', '.join(food_items[:3])}"
            if len(food_items) > 3:
                base_description += " and other nutritious foods"
        else:
            base_description = "Nutritious healthy food arrangement"

        # Add setting and presentation details
        base_description += " beautifully arranged on a clean plate or wooden table, with fresh ingredients, vibrant colors"

    elif "fitness" in topic_lower or "exercise" in topic_lower or "workout" in topic_lower:
        # Check for specific exercises
        exercises = extract_exercise_types(text)
        if exercises:
            base_description = f"{', '.join(exercises[:2])} exercise"
        else:
            base_description = "Fitness workout session"

        # Add setting and presentation details
        base_description += " in a modern gym or outdoor setting, showing proper form, energetic atmosphere"

    elif "medical" in topic_lower or "health" in topic_lower or "wellness" in topic_lower:
        # Create a medical/wellness setting
        if any(term in text.lower() for term in ["doctor", "physician", "hospital", "clinic", "medical"]):
            base_description = "Professional healthcare setting with medical equipment"
        else:
            base_description = "Wellness and health concept with natural elements"

        # Add health-specific elements if found
        if health_terms:
            base_description += f", focusing on {', '.join(health_terms[:2])}"

    elif "mental health" in topic_lower or "psychology" in topic_lower:
        base_description = "Serene mental wellness concept with calming elements, meditation or mindfulness visualization"

        # Add specific mental health elements if found
        mental_terms = [term for term in health_terms if term in ["stress", "anxiety", "depression", "mindfulness", "meditation", "therapy", "counseling", "relaxation"]]
        if mental_terms:
            base_description += f", focusing on {', '.join(mental_terms[:2])}"

    else:
        # Generic health topic
        base_description = "Health and wellness concept with natural elements and positive atmosphere"
        if health_terms:
            base_description += f", focusing on {', '.join(health_terms[:2])}"

    # Create the final prompt with style-specific enhancements
    image_style_lower = image_style.lower()
    image_style_normalized = image_style.lower().replace(" ", "").replace("-", "")

    # Print debug information
    print(f"Health topic: Looking up style template for: {image_style_normalized}")

    # Health-specific style templates with normalized keys
    health_style_prompts = {
        "photorealistic": f"Highly detailed, photorealistic 8K image of {base_description}. Clean, bright, professional medical or wellness photography, perfect lighting, ultra-detailed.",

        "cinematic": f"Cinematic 8K image of {base_description}. Professional lighting, inspirational atmosphere, high-quality production, health documentary style.",

        "infographic": f"Clean, informative infographic style illustration of {base_description}. Educational, clear visual elements, professional medical illustration style, informative design.",

        "medicalillustration": f"Professional medical illustration of {base_description}. Anatomically correct, educational, detailed, clean background, scientific accuracy.",

        "anime": f"Japanese anime style illustration of {base_description}. Vibrant colors, clean linework, studio Ghibli inspired, detailed character design, high quality anime aesthetic.",
    }

    # Special handling for anime and ghibli styles
    if "anime" in image_style_lower:
        print("Anime style detected for health topic, using anime template")
        prompt = f"Japanese anime style illustration of {base_description}. Vibrant colors, clean linework, studio Ghibli inspired, detailed character design, high quality anime aesthetic."
    elif "ghibli" in image_style_lower:
        print("Ghibli style detected for health topic, using ghibli template")
        prompt = f"Studio Ghibli style illustration of {base_description}. Soft watercolor-like textures, gentle color palette, whimsical characters, detailed natural environments, dreamy atmospheric lighting, hand-drawn animation quality, Miyazaki-inspired artistic sensibility."
    else:
        # Get the prompt for the specified style, or use a default if not found
        prompt = health_style_prompts.get(image_style_normalized)

        if prompt:
            print(f"Found health style template for: {image_style_normalized}")
        else:
            print(f"No exact match found for health style: {image_style_normalized}, using default template")
            prompt = f"Clean, professional image of {base_description}. Health-focused, educational, positive visualization."

    # Add a reminder for no text
    prompt += " NO TEXT, no words, no writing, no captions, no labels."

    return prompt

def extract_health_terms(text: str) -> List[str]:
    """
    Extract health-related terms from text.

    Args:
        text: The text to analyze

    Returns:
        List of health terms
    """
    text_lower = text.lower()

    # Common health categories and terms
    health_categories = {
        "nutrition": ["vitamin", "mineral", "protein", "carbohydrate", "fat", "nutrient", "antioxidant", "fiber", "diet",
                     "calorie", "metabolism", "digestion", "organic", "superfood", "supplement", "nutrition", "nutritious"],

        "fitness": ["exercise", "workout", "training", "cardio", "strength", "flexibility", "endurance", "muscle", "weight",
                   "fitness", "gym", "yoga", "pilates", "running", "swimming", "cycling", "aerobic", "anaerobic"],

        "medical": ["health", "medical", "doctor", "hospital", "clinic", "treatment", "therapy", "medication", "drug",
                   "prescription", "diagnosis", "symptom", "condition", "disease", "illness", "recovery", "prevention",
                   "checkup", "examination", "screening", "test", "surgery", "procedure", "immune", "immunity"],

        "mental health": ["mental", "psychological", "emotional", "cognitive", "brain", "mind", "stress", "anxiety",
                         "depression", "therapy", "counseling", "psychiatry", "psychology", "mindfulness", "meditation",
                         "relaxation", "wellbeing", "wellness", "self-care", "mood", "behavior", "sleep"]
    }

    # Extract all health terms found in the text
    found_terms = []
    for category, terms in health_categories.items():
        for term in terms:
            if term in text_lower and term not in found_terms:
                found_terms.append(term)

    return found_terms

def extract_food_items(text: str) -> List[str]:
    """
    Extract food items from text.

    Args:
        text: The text to analyze

    Returns:
        List of food items
    """
    text_lower = text.lower()

    # Common food categories
    food_categories = {
        "fruits": ["apple", "banana", "orange", "grape", "strawberry", "blueberry", "raspberry", "blackberry",
                  "kiwi", "pineapple", "mango", "peach", "pear", "plum", "watermelon", "melon", "cherry", "lemon",
                  "lime", "avocado", "coconut", "fig", "date", "apricot", "pomegranate", "grapefruit", "berries", "fruit"],

        "vegetables": ["carrot", "broccoli", "spinach", "kale", "lettuce", "cabbage", "cauliflower", "cucumber",
                      "tomato", "potato", "sweet potato", "onion", "garlic", "pepper", "eggplant", "zucchini",
                      "squash", "pumpkin", "corn", "pea", "bean", "lentil", "asparagus", "celery", "radish",
                      "beetroot", "mushroom", "vegetable", "greens", "salad"],

        "grains": ["rice", "wheat", "oat", "barley", "quinoa", "corn", "bread", "pasta", "cereal", "grain",
                  "flour", "oatmeal", "granola", "muesli", "couscous", "bulgur", "whole grain"],

        "proteins": ["chicken", "beef", "pork", "lamb", "fish", "salmon", "tuna", "shrimp", "egg", "tofu",
                    "tempeh", "seitan", "bean", "lentil", "chickpea", "nut", "seed", "protein", "meat", "poultry"],

        "dairy": ["milk", "cheese", "yogurt", "butter", "cream", "dairy", "kefir", "whey", "curd", "cottage cheese"],

        "nuts_seeds": ["almond", "walnut", "cashew", "pistachio", "pecan", "hazelnut", "peanut", "nut",
                      "seed", "sunflower seed", "pumpkin seed", "chia seed", "flax seed", "sesame seed"],

        "healthy_fats": ["olive oil", "coconut oil", "avocado oil", "oil", "fat", "omega", "fatty acid"],

        "herbs_spices": ["herb", "spice", "basil", "oregano", "thyme", "rosemary", "mint", "cilantro",
                        "parsley", "dill", "sage", "cinnamon", "turmeric", "ginger", "garlic", "cumin",
                        "paprika", "pepper", "salt"]
    }

    # Extract all food items found in the text
    found_foods = []
    for category, foods in food_categories.items():
        for food in foods:
            if food in text_lower and food not in found_foods:
                found_foods.append(food)

    return found_foods

def extract_exercise_types(text: str) -> List[str]:
    """
    Extract exercise types from text.

    Args:
        text: The text to analyze

    Returns:
        List of exercise types
    """
    text_lower = text.lower()

    # Common exercise categories
    exercise_categories = {
        "cardio": ["running", "jogging", "walking", "cycling", "swimming", "rowing", "elliptical", "stair climbing",
                  "jumping rope", "aerobics", "dancing", "hiking", "cardio", "cardiovascular", "aerobic"],

        "strength": ["weightlifting", "resistance training", "bodyweight", "strength training", "lifting", "weights",
                    "dumbbell", "barbell", "kettlebell", "machine", "bench press", "squat", "deadlift", "lunge",
                    "push-up", "pull-up", "strength", "muscle", "muscular", "resistance"],

        "flexibility": ["stretching", "yoga", "pilates", "tai chi", "mobility", "flexibility", "range of motion",
                       "dynamic stretching", "static stretching", "stretch"],

        "sports": ["basketball", "football", "soccer", "tennis", "golf", "baseball", "volleyball", "hockey",
                  "rugby", "cricket", "badminton", "table tennis", "martial arts", "boxing", "wrestling",
                  "sport", "athletic", "athletics", "game"],

        "functional": ["functional training", "crossfit", "hiit", "high intensity", "interval training",
                      "circuit training", "boot camp", "plyometrics", "agility", "balance", "core", "functional"]
    }

    # Extract all exercise types found in the text
    found_exercises = []
    for category, exercises in exercise_categories.items():
        for exercise in exercises:
            if exercise in text_lower and exercise not in found_exercises:
                found_exercises.append(exercise)

    return found_exercises

def generate_islamic_prompt(text: str, image_style: str = "cinematic") -> str:
    """
    Generate a specialized image prompt for Islamic content.

    Args:
        text: The text to analyze
        image_style: Style for the image

    Returns:
        Islamic-focused image prompt
    """
    text_lower = text.lower()

    # Determine the type of Islamic content
    content_type = "general"

    # Check for hadith content
    if any(term in text_lower for term in ["hadith", "saying", "narration", "reported", "prophet muhammad", "prophet said"]):
        content_type = "hadith"
    # Check for historical content
    elif any(term in text_lower for term in ["history", "historical", "civilization", "empire", "caliphate", "dynasty"]):
        content_type = "history"
    # Check for ethical content
    elif any(term in text_lower for term in ["ethics", "moral", "character", "behavior", "conduct", "manners"]):
        content_type = "ethics"
    # Check for Quranic content
    elif any(term in text_lower for term in ["quran", "verse", "surah", "ayah", "revelation", "scripture"]):
        content_type = "quran"

    # Create a base description based on the content type
    if content_type == "hadith":
        base_description = "Islamic knowledge concept representing hadith and prophetic teachings"
    elif content_type == "history":
        base_description = "Historical Islamic scene with architectural elements"
    elif content_type == "ethics":
        base_description = "Islamic moral and ethical concept visualization"
    elif content_type == "quran":
        base_description = "Symbolic representation of Quranic wisdom and guidance"
    else:
        # Generic Islamic content
        base_description = "Islamic knowledge concept with symbolic elements"

    # Add visual elements common to Islamic imagery
    base_description += ". Featuring geometric patterns, calligraphy-inspired design elements, architectural motifs, and symbolic imagery"

    # Create the final prompt with style-specific enhancements
    image_style_lower = image_style.lower()
    image_style_normalized = image_style.lower().replace(" ", "").replace("-", "")

    # Islamic-specific style templates
    islamic_style_prompts = {
        "photorealistic": f"Highly detailed, photorealistic 8K image of {base_description}. Professional photography with perfect lighting, rich details of Islamic art and architecture.",

        "cinematic": f"Cinematic 8K image of {base_description}. Professional lighting, dramatic atmosphere, high-quality production with rich cultural elements.",

        "illustration": f"Detailed illustration of {base_description}. Artistic rendering with intricate patterns, symbolic elements, and cultural authenticity.",

        "minimalist": f"Clean, minimalist design representing {base_description}. Simple geometric patterns, limited color palette, elegant composition with essential elements only.",

        "watercolor": f"Delicate watercolor painting of {base_description}. Soft color washes, flowing transitions, subtle bleeding edges, translucent layers on textured paper."
    }

    # Get the prompt for the specified style, or use a default if not found
    prompt = islamic_style_prompts.get(image_style_normalized)

    if not prompt:
        # Default to cinematic if no specific style match
        prompt = f"Cinematic 8K image of {base_description}. Professional lighting, dramatic atmosphere, high-quality production with rich cultural elements."

    # Add a reminder for no text and no faces/people
    prompt += " NO TEXT, no words, no writing, no captions, no labels. NO FACES, no people, no human figures, no depictions of prophets or religious figures."

    return prompt

def generate_specialized_prompt(text: str, story_type: str, image_style: str = "cinematic") -> str:
    """
    Generate a specialized image prompt based on the story type.

    Args:
        text: The text to analyze
        story_type: Type of story
        image_style: Style for the image

    Returns:
        Specialized image prompt
    """
    # Convert to lowercase for case-insensitive matching
    story_type_lower = story_type.lower()

    # Check for health-related topics
    if any(health_topic in story_type_lower for health_topic in ["health", "nutrition", "fitness", "medical", "wellness", "diet"]):
        return generate_health_topic_prompt(text, story_type_lower, image_style)

    # Check for Islamic content
    if any(islamic_topic in story_type_lower for islamic_topic in ["islamic", "islam", "hadith", "quran", "muslim"]):
        return generate_islamic_prompt(text, image_style)

    # For all other topics, use the standard image prompt generator
    return generate_image_prompt(text, story_type, image_style)

def enhance_image_prompt(text: str, story_type: str = "general", image_style: str = "cinematic",
                         is_custom_script: bool = False, is_custom_title: bool = False) -> str:
    """
    Main entry point for enhanced image prompt generation.

    This function analyzes the input text and generates an appropriate image prompt
    based on the content, story type, and desired image style.

    Args:
        text: The text to analyze (paragraph, scene description, etc.)
        story_type: Type of story (e.g., "scary", "love", "health", etc.)
        image_style: Desired visual style (e.g., "cinematic", "anime", "photorealistic")
        is_custom_script: Whether the text is from a custom script
        is_custom_title: Whether the text is a custom title

    Returns:
        Enhanced image prompt optimized for the content
    """
    # Clean and normalize the input text
    cleaned_text = text.strip()
    if not cleaned_text:
        return "High-quality detailed image with professional lighting and composition. NO TEXT, no words, no writing."

    # Map the story type to ensure consistency
    mapped_story_type = map_story_type(story_type)

    # Handle custom titles differently - they need more context
    if is_custom_title:
        # For custom titles, we need to be more descriptive and scene-focused
        title_words = cleaned_text.split()

        # Create a more descriptive prompt based on the title
        if any(word.lower() in ["journey", "adventure", "quest", "path", "road", "travel"] for word in title_words):
            return f"Cinematic landscape scene representing '{cleaned_text}'. Epic natural vista, dramatic lighting, inspiring view, detailed environment without people. Professional photography, high-quality production. NO TEXT, no words, no writing."

        elif any(word.lower() in ["mind", "thought", "dream", "imagination", "vision", "idea", "concept"] for word in title_words):
            return f"Abstract conceptual scene representing '{cleaned_text}'. Surreal dreamlike environment, ethereal atmosphere, symbolic elements, no people or faces. Professional artistic composition, high-quality production. NO TEXT, no words, no writing."

        elif any(word.lower() in ["love", "heart", "romance", "passion", "affection", "emotion"] for word in title_words):
            return f"Romantic atmospheric scene representing '{cleaned_text}'. Beautiful natural setting, warm lighting, intimate mood, symbolic elements without people. Professional photography, high-quality production. NO TEXT, no words, no writing."

        elif any(word.lower() in ["power", "strength", "force", "energy", "might", "vigor"] for word in title_words):
            return f"Powerful dramatic scene representing '{cleaned_text}'. Dynamic natural elements, intense lighting, strong visual impact, no people. Professional photography, high-quality production. NO TEXT, no words, no writing."

        elif any(word.lower() in ["health", "wellness", "nutrition", "diet", "fitness"] for word in title_words):
            return f"Clean, bright scene representing '{cleaned_text}'. Fresh fruits, vegetables, or fitness equipment arranged artistically, vibrant colors, health-focused imagery without people. Professional photography, high-quality production. NO TEXT, no words, no writing."

        elif any(word.lower() in ["pet", "dog", "cat", "animal", "bird", "fish"] for word in title_words):
            return f"Professional pet care scene representing '{cleaned_text}'. Beautiful pet environment, toys, or accessories arranged artistically without showing actual animals. Warm, inviting colors, high-quality production. NO TEXT, no words, no writing."

        elif any(word.lower() in ["tech", "technology", "digital", "computer", "software", "hardware", "internet"] for word in title_words):
            return f"Modern technology scene representing '{cleaned_text}'. Clean, minimalist setting with sleek devices or digital interfaces. Futuristic aesthetic, blue-toned lighting, no people. Professional technology photography, high-quality production. NO TEXT, no words, no writing."

        else:
            return f"Cinematic scene representing '{cleaned_text}'. Detailed environment without people or faces, atmospheric lighting, meaningful composition. Professional photography, high-quality production. NO TEXT, no words, no writing."

    # Handle pet tips content specially
    if mapped_story_type == "pet_tips":
        # Find which pet types are mentioned
        pet_types = []
        for pet in ["dog", "cat", "bird", "fish", "hamster", "rabbit", "reptile"]:
            if pet in cleaned_text.lower():
                pet_types.append(pet)

        # If no specific pet type is found, use generic "pet"
        if not pet_types:
            pet_types = ["pet"]

        # Extract activities or topics
        activities = []
        for activity in ["training", "grooming", "feeding", "health", "care", "play", "exercise", "sleep", "behavior", "adoption", "rescue"]:
            if activity in cleaned_text.lower():
                activities.append(activity)

        # If no specific activities found, use generic "care"
        if not activities:
            activities = ["care"]

        # Create a specialized pet prompt
        pet_type_str = " and ".join(pet_types)
        activity_str = " and ".join(activities)

        return f"Professional pet care scene for {pet_type_str} {activity_str}. Clean, bright setting with pet accessories, toys, or care items. No actual animals shown, just their environment and supplies. Warm, inviting colors, high-quality production. NO TEXT, no words, no writing."

    # Handle health-related content specially
    if mapped_story_type in ["health", "nutrition", "fitness"]:
        # Check for specific health topics in the text
        text_lower = cleaned_text.lower()

        if "apple" in text_lower or "fruit" in text_lower:
            return f"Clean, bright image of fresh apples or assorted fruits on a light background. Vibrant colors, healthy food photography style, no people or faces. Professional lighting, high-quality production. NO TEXT."

        if "vegetable" in text_lower or "vegetables" in text_lower:
            return f"Artistic arrangement of fresh vegetables with vibrant colors. Healthy food photography style, no people or faces. Professional lighting, high-quality production. NO TEXT."

        if "exercise" in text_lower or "workout" in text_lower or "fitness" in text_lower:
            return f"Stylish fitness equipment or workout space with dramatic lighting. Modern fitness aesthetic, no people or faces. Professional photography, high-quality production. NO TEXT."

        if "sleep" in text_lower or "rest" in text_lower:
            return f"Serene bedroom scene with comfortable bedding in soft lighting. Peaceful sleep environment, no people or faces. Professional interior photography, high-quality production. NO TEXT."

        if "meditation" in text_lower or "mindfulness" in text_lower:
            return f"Zen-inspired meditation space with calming elements like candles or plants. Peaceful atmosphere, no people or faces. Professional photography, high-quality production. NO TEXT."

        # Generic health prompt if no specific topics identified
        return f"Clean, bright image representing health and wellness. Natural elements, vibrant colors, healthy lifestyle setting without people or faces. Professional photography, high-quality production. NO TEXT."

    # Handle technology content specially
    if mapped_story_type == "technology":
        # Find which tech types are mentioned
        text_lower = cleaned_text.lower()
        tech_types = []
        for tech in ["computer", "laptop", "smartphone", "tablet", "gadget", "software", "hardware", "internet", "ai", "artificial intelligence", "robot", "automation", "digital", "code", "programming", "app", "application", "device"]:
            if tech in text_lower:
                tech_types.append(tech)

        # If specific tech types found, create a specialized prompt
        if tech_types:
            tech_type_str = " and ".join(tech_types[:3])  # Limit to 3 types to keep prompt focused
            return f"Modern technology scene featuring {tech_type_str}. Clean, minimalist setting with sleek devices or digital interfaces. Futuristic aesthetic, blue-toned lighting, no people or faces. Professional technology photography, high-quality production. NO TEXT."

        # Generic tech prompt if no specific topics identified
        return f"Modern technology scene with sleek devices and digital interfaces. Futuristic aesthetic, blue-toned lighting, no people or faces. Professional technology photography, high-quality production. NO TEXT."

    # Handle food content specially
    if mapped_story_type == "food":
        # Find which food types are mentioned
        text_lower = cleaned_text.lower()
        food_types = []
        for food in ["vegetable", "fruit", "meat", "fish", "seafood", "grain", "pasta", "rice", "bread", "dessert", "cake", "cookie", "pie", "pastry", "chocolate", "coffee", "tea", "juice", "smoothie"]:
            if food in text_lower:
                food_types.append(food)

        # If specific food types found, create a specialized prompt
        if food_types:
            food_type_str = " and ".join(food_types[:3])  # Limit to 3 types to keep prompt focused
            return f"Gourmet food photography featuring {food_type_str}. Artistic food arrangement, professional styling, soft natural lighting. No people or faces. High-quality culinary photography, professional production. NO TEXT."

        # Generic food prompt if no specific topics identified
        return f"Gourmet food photography with artistic food arrangement. Professional styling, soft natural lighting, no people or faces. High-quality culinary photography, professional production. NO TEXT."

    # Handle Islamic content specially
    if mapped_story_type == "islamic":
        # Find which Islamic concepts are mentioned
        text_lower = cleaned_text.lower()

        # Check for hadith content
        if any(term in text_lower for term in ["hadith", "saying", "narration", "reported", "prophet muhammad", "prophet said"]):
            return f"Islamic knowledge concept representing hadith and prophetic teachings. Featuring geometric patterns, calligraphy-inspired design elements, architectural motifs, and symbolic imagery. Professional lighting, dramatic atmosphere, high-quality production with rich cultural elements. NO TEXT, no words, no writing, no captions, no labels. NO FACES, no people, no human figures, no depictions of prophets or religious figures."

        # Check for historical content
        if any(term in text_lower for term in ["history", "historical", "civilization", "empire", "caliphate", "dynasty"]):
            return f"Historical Islamic scene with architectural elements. Ancient mosque, palace, or madrasa with intricate geometric patterns, arches, domes, and minarets. Professional lighting, dramatic atmosphere, high-quality production with rich cultural elements. NO TEXT, no words, no writing, no captions, no labels. NO FACES, no people, no human figures."

        # Check for ethical content
        if any(term in text_lower for term in ["ethics", "moral", "character", "behavior", "conduct", "manners"]):
            return f"Islamic moral and ethical concept visualization. Symbolic imagery with geometric patterns, calligraphy-inspired design elements, and meaningful visual metaphors. Professional lighting, dramatic atmosphere, high-quality production with rich cultural elements. NO TEXT, no words, no writing, no captions, no labels. NO FACES, no people, no human figures."

        # Check for Quranic content
        if any(term in text_lower for term in ["quran", "verse", "surah", "ayah", "revelation", "scripture"]):
            return f"Symbolic representation of Quranic wisdom and guidance. Elegant geometric patterns, calligraphy-inspired design elements, and meaningful visual metaphors. Professional lighting, dramatic atmosphere, high-quality production with rich cultural elements. NO TEXT, no words, no writing, no captions, no labels. NO FACES, no people, no human figures."

        # Generic Islamic content
        return f"Islamic knowledge concept with symbolic elements. Featuring geometric patterns, calligraphy-inspired design elements, architectural motifs, and symbolic imagery. Professional lighting, dramatic atmosphere, high-quality production with rich cultural elements. NO TEXT, no words, no writing, no captions, no labels. NO FACES, no people, no human figures, no depictions of prophets or religious figures."

    # For custom scripts, use specialized prompt generation
    if is_custom_script:
        return generate_image_prompt_for_custom_script(cleaned_text, story_type, image_style)

    # For all other cases, use the standard image prompt generator
    return generate_image_prompt(cleaned_text, mapped_story_type, image_style)

# Example usage
if __name__ == "__main__":
    # Test with different types of content

    # Example 1: Action scene
    action_scene = "A group of armored knights charges across a misty battlefield at dawn, swords raised high, as arrows fly through the air."
    print("\nAction Scene Example:")
    print(enhance_image_prompt(action_scene, "action", "cinematic"))

    # Example 2: Emotional scene
    emotional_scene = "A lonely old man sits on a bench in an empty park during autumn, with leaves falling around him."
    print("\nEmotional Scene Example:")
    print(enhance_image_prompt(emotional_scene, "drama", "cinematic"))

    # Example 3: Fantasy story
    fantasy_scene = "A young wizard with a glowing staff stands at the edge of a floating island, gazing at a giant dragon flying in the sky."
    print("\nFantasy Scene Example:")
    print(enhance_image_prompt(fantasy_scene, "fantasy", "fantasy-vibrant"))

    # Example 4: Health topic
    health_topic = "Apples are rich in antioxidants and fiber, which can help reduce the risk of chronic diseases and improve digestive health."
    print("\nHealth Topic Example:")
    print(enhance_image_prompt(health_topic, "health", "photorealistic"))

    # Example 5: Custom title
    custom_title = "The Journey Within"
    print("\nCustom Title Example:")
    print(enhance_image_prompt(custom_title, "motivational", "cinematic", is_custom_title=True))
