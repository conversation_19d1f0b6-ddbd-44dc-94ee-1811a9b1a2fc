import os
import asyncio
import tempfile
import threading
import time
import subprocess
import platform
import edge_tts
import ai_client
from audio_generator import clean_text_for_tts
from elevenlabs_client import elevenlabs_client

# Sample text for voice preview
PREVIEW_TEXT = "This is a sample of my voice. How does it sound?"

# Cache directory for storing voice samples
CACHE_DIR = os.path.join(tempfile.gettempdir(), "voice_preview_cache")
os.makedirs(CACHE_DIR, exist_ok=True)

# Track currently playing audio
current_playback = None
is_playing = False

def get_cache_path(voice_name, tts_model):
    """Get the path for a cached voice sample"""
    # Create a filename based on voice name and TTS model
    safe_name = voice_name.replace(":", "_").replace("/", "_").replace("\\", "_")
    return os.path.join(CACHE_DIR, f"{safe_name}_{tts_model}.mp3")

async def generate_edge_tts_preview(voice_name, output_file, max_retries=5):
    """Generate a preview audio file using Edge TTS"""
    # Clean the text before sending to TTS
    cleaned_text = clean_text_for_tts(PREVIEW_TEXT)

    # Ensure voice name has proper format (locale-Language-VoiceNameNeural)
    if not voice_name.startswith("en-"):
        print(f"Warning: Voice name '{voice_name}' doesn't have locale prefix. Assuming en-US.")
        voice_name = f"en-US-{voice_name}"

    # Make sure the voice name ends with "Neural" if it doesn't already
    if not voice_name.endswith("Neural"):
        voice_name = f"{voice_name}Neural"

    # Retry logic for the selected voice
    for attempt in range(max_retries):
        try:
            if attempt > 0:
                print(f"Retry attempt {attempt}/{max_retries-1} for voice preview: {voice_name}")
            else:
                print(f"Generating preview for voice: {voice_name}")

            # Configure communication with Edge TTS
            communicate = edge_tts.Communicate(cleaned_text, voice_name)

            # Save audio to file
            await communicate.save(output_file)
            return True
        except Exception as e:
            print(f"Error generating Edge TTS preview (attempt {attempt+1}/{max_retries}): {str(e)}")
            # Add a small delay before retrying
            await asyncio.sleep(1)

    # If we've exhausted all retries with the selected voice, try the default voice
    try:
        default_voice = "en-US-AriaNeural"
        if voice_name != default_voice:
            print(f"All {max_retries} attempts with selected voice failed. Trying with default voice: {default_voice}")
            communicate = edge_tts.Communicate(cleaned_text, default_voice)
            await communicate.save(output_file)
            print(f"Generated preview with default voice instead")
            return True
    except Exception as fallback_error:
        print(f"Error with fallback voice: {str(fallback_error)}")

    return False

def generate_openai_tts_preview(voice_name, output_file):
    """Generate a preview audio file using OpenAI TTS"""
    try:
        # Clean the text before sending to TTS
        cleaned_text = clean_text_for_tts(PREVIEW_TEXT)

        # Always use the OpenAI client specifically for TTS, regardless of current provider
        # First save the current provider
        current_provider = ai_client.get_current_provider()
        print(f"Current AI provider: {current_provider}")

        # Temporarily switch to OpenAI
        if current_provider != "openai":
            print("Temporarily switching to OpenAI for TTS preview")
            ai_client.set_provider("openai")

        # Get the OpenAI client
        client = ai_client.get_current_client()
        if not client:
            print("OpenAI client not available")
            # Restore original provider
            if current_provider != "openai":
                ai_client.set_provider(current_provider)
            return False

        print(f"Generating OpenAI TTS preview for voice: {voice_name}")

        # Generate audio
        result = client.audio.speech.create(
            model="tts-1",
            voice=voice_name,
            input=cleaned_text,
            response_format="mp3"
        )

        # Save the audio content to the output file
        with open(output_file, "wb") as audio_file:
            audio_file.write(result.content)

        # Restore original provider
        if current_provider != "openai":
            print(f"Restoring original provider: {current_provider}")
            ai_client.set_provider(current_provider)

        return True
    except Exception as e:
        print(f"Error generating OpenAI TTS preview: {str(e)}")

        # Try with a default voice if the specified voice fails
        try:
            default_voice = "alloy"
            if voice_name != default_voice:
                print(f"Trying with default voice: {default_voice}")
                result = client.audio.speech.create(
                    model="tts-1",
                    voice=default_voice,
                    input=cleaned_text,
                    response_format="mp3"
                )

                with open(output_file, "wb") as audio_file:
                    audio_file.write(result.content)

                print(f"Generated preview with default voice instead")

                # Restore original provider
                if current_provider != "openai":
                    print(f"Restoring original provider: {current_provider}")
                    ai_client.set_provider(current_provider)

                return True
        except Exception as fallback_error:
            print(f"Error with fallback voice: {str(fallback_error)}")

        # Restore original provider in case of error
        if current_provider != "openai":
            try:
                print(f"Restoring original provider after error: {current_provider}")
                ai_client.set_provider(current_provider)
            except:
                pass

        return False

def generate_elevenlabs_preview(voice_id, output_file):
    """Generate a preview audio file using ElevenLabs TTS"""
    try:
        # Clean the text before sending to TTS
        cleaned_text = clean_text_for_tts(PREVIEW_TEXT)

        # Check if ElevenLabs client is available
        if not elevenlabs_client.is_available:
            print("ElevenLabs API key not set. Cannot generate preview.")
            return False

        print(f"Generating ElevenLabs preview for voice ID: {voice_id}")

        # Generate audio using ElevenLabs client
        success = elevenlabs_client.generate_preview(
            voice_id=voice_id,
            output_file=output_file,
            preview_text=cleaned_text
        )

        if success:
            print(f"ElevenLabs preview generated and saved to {output_file}")
            return True
        else:
            print("Failed to generate ElevenLabs preview")
            return False

    except Exception as e:
        print(f"Error generating ElevenLabs preview: {str(e)}")
        return False

def generate_preview(voice_name, tts_model, force_new=False):
    """
    Generate a voice preview sample

    Args:
        voice_name: Name of the voice to preview
        tts_model: TTS model to use ("OpenAI", "Voicely", or "ElevenLabs")
        force_new: Whether to force generation of a new sample even if cached

    Returns:
        Path to the audio file or None if generation failed
    """
    cache_path = get_cache_path(voice_name, tts_model)

    # Check if we have a cached version and should use it
    if os.path.exists(cache_path) and not force_new:
        return cache_path

    # Generate a new preview
    try:
        print(f"Generating preview for voice '{voice_name}' using TTS model '{tts_model}'")

        if tts_model == "Voicely":
            success = asyncio.run(generate_edge_tts_preview(voice_name, cache_path, max_retries=5))
        elif tts_model == "OpenAI":
            # Make sure we're using a valid OpenAI voice name
            valid_openai_voices = ["alloy", "echo", "fable", "onyx", "nova", "shimmer"]
            if voice_name not in valid_openai_voices:
                print(f"Warning: '{voice_name}' is not a valid OpenAI voice. Using 'alloy' instead.")
                voice_name = "alloy"

            success = generate_openai_tts_preview(voice_name, cache_path)
        elif tts_model == "ElevenLabs":
            # For ElevenLabs, voice_name is the voice ID
            success = generate_elevenlabs_preview(voice_name, cache_path)
        else:
            print(f"Unsupported TTS model: {tts_model}")
            # Fallback to Voicely if the model is not recognized
            print(f"Falling back to Voicely TTS")
            success = asyncio.run(generate_edge_tts_preview(voice_name, cache_path, max_retries=5))

        if success and os.path.exists(cache_path):
            return cache_path
        else:
            return None
    except Exception as e:
        print(f"Error generating preview: {str(e)}")
        return None

def play_preview(audio_path, callback=None):
    """
    Play a voice preview sample using system default player

    Args:
        audio_path: Path to the audio file to play
        callback: Function to call when playback completes
    """
    global current_playback, is_playing

    # Stop any currently playing audio
    stop_preview()

    # Start playback using system commands
    try:
        # Use appropriate command based on platform
        if platform.system() == "Windows":
            # Use Windows Media Player in background
            process = subprocess.Popen(
                ["start", "", audio_path],
                shell=True,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
        elif platform.system() == "Darwin":  # macOS
            process = subprocess.Popen(
                ["afplay", audio_path],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
        else:  # Linux and others
            # Try to use any available player
            for player in ["xdg-open", "paplay", "aplay"]:
                try:
                    process = subprocess.Popen(
                        [player, audio_path],
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL
                    )
                    break
                except FileNotFoundError:
                    continue
            else:
                print("No suitable audio player found")
                return False

        is_playing = True

        # Create a thread to monitor playback and call the callback when done
        def monitor_playback():
            global is_playing
            # Wait for process to complete or timeout after 10 seconds
            try:
                if platform.system() != "Windows":  # Windows uses 'start' which returns immediately
                    process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                # Kill the process if it takes too long
                try:
                    process.kill()
                except:
                    pass

            # Set a small delay to allow audio to finish playing
            time.sleep(3)
            is_playing = False
            if callback:
                callback()

        current_playback = threading.Thread(target=monitor_playback)
        current_playback.daemon = True
        current_playback.start()

        return True
    except Exception as e:
        print(f"Error playing preview: {str(e)}")
        is_playing = False
        return False

def stop_preview():
    """Stop any currently playing preview"""
    global is_playing, current_playback
    if is_playing:
        # We can't easily stop the system player, but we can mark it as stopped
        is_playing = False
        # If there's a callback thread, we'll just let it finish naturally

def is_preview_playing():
    """Check if a preview is currently playing"""
    return is_playing

def generate_and_play_preview(voice_name, tts_model, callback=None, force_new=False):
    """
    Generate and play a voice preview

    Args:
        voice_name: Name of the voice to preview
        tts_model: TTS model to use ("OpenAI", "Voicely", or "ElevenLabs")
        callback: Function to call when playback completes
        force_new: Whether to force generation of a new sample

    Returns:
        True if playback started successfully, False otherwise
    """
    # Generate the preview in a separate thread to avoid blocking the UI
    def generate_and_play_thread():
        audio_path = generate_preview(voice_name, tts_model, force_new)
        if audio_path:
            play_preview(audio_path, callback)
            return True
        return False

    thread = threading.Thread(target=generate_and_play_thread)
    thread.daemon = True
    thread.start()
    return True

def clear_cache():
    """Clear the voice preview cache"""
    try:
        for file in os.listdir(CACHE_DIR):
            file_path = os.path.join(CACHE_DIR, file)
            if os.path.isfile(file_path):
                os.unlink(file_path)
        return True
    except Exception as e:
        print(f"Error clearing cache: {str(e)}")
        return False
