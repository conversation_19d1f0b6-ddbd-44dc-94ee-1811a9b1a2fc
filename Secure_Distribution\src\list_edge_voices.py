import asyncio
import edge_tts
import json

async def main():
    # Get all available voices
    voices = await edge_tts.list_voices()
    
    # Filter to only English voices
    english_voices = [v for v in voices if v["Locale"].startswith("en-")]
    
    print(f"Found {len(english_voices)} English voices:")
    
    # Organize voices by locale
    organized_voices = {}
    for voice in english_voices:
        locale = voice["Locale"]
        gender = voice["Gender"]
        name = voice["ShortName"]
        friendly_name = voice["FriendlyName"]
        
        if locale not in organized_voices:
            organized_voices[locale] = {"Male": [], "Female": []}
        
        organized_voices[locale][gender].append({
            "short_name": name,
            "friendly_name": friendly_name
        })
    
    # Print organized voices
    for locale, genders in organized_voices.items():
        print(f"\n{locale}:")
        for gender, voices in genders.items():
            if voices:
                print(f"  {gender}:")
                for voice in voices:
                    print(f"    {voice['short_name']} - {voice['friendly_name']}")
    
    # Format for direct use in code
    voices_for_code = []
    for voice in english_voices:
        name = voice["ShortName"]
        friendly_name = voice["FriendlyName"].replace("(", "-").replace(")", "")
        voices_for_code.append(f'"{name}", # {friendly_name}')
    
    print("\nVoices for code usage:")
    print("[\n    " + ",\n    ".join(voices_for_code) + "\n]")
    
    # Write to JSON file for easy use
    with open("english_edge_voices.json", "w") as f:
        json.dump(english_voices, f, indent=2)
    
    print("\nVoice details saved to english_edge_voices.json")

if __name__ == "__main__":
    asyncio.run(main()) 