from utils import (
    call_openai_api,
    create_empty_storyboard,
    load_config,
    STORY_TYPE_HASHTAGS
)
import re
import logging
import json
from json_cleaner import clean_json_string
import traceback
from datetime import datetime
from typing import Dict, Any, List, Tuple
from utils import STORY_TYPE_HASHTAGS

# Configure logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def get_story_type_guidelines(story_type: str) -> str:
    story_type_guidelines = {
        "love": '''
        1. Emotional connection: Create a story that explores genuine emotional connection between people.
        2. Relationship dynamics: Show the complexities, joys, and challenges of romantic relationships.
        3. Character development: Develop characters whose feelings and actions feel authentic and relatable.
        4. Emotional arc: Create a clear emotional journey with meaningful moments of connection.
        5. Universal themes: Incorporate universal themes about love that resonate with a wide audience.
        6. Authenticity: Ensure the emotions and situations feel real and relatable, not clichéd.
        7. Emotional impact: Aim for content that genuinely moves the audience emotionally.
        ''',
        "philosophy": '''
        1. Core concept: Focus on a single philosophical idea or question that's accessible to a general audience.
        2. Real-world application: Show how philosophical concepts directly apply to everyday life situations.
        3. Relatable examples: Use concrete, relatable examples that viewers can connect with personally.
        4. Simple language: Explain philosophical concepts in simple, jargon-free language.
        5. Visual storytelling: Create a narrative that visually demonstrates the philosophical principle.
        6. Practical wisdom: Emphasize practical wisdom and actionable insights rather than abstract theory.
        7. Emotional connection: Connect the philosophical idea to emotions or experiences viewers already understand.
        8. Open-ended conclusion: End with a thought-provoking question that encourages personal reflection.

        Create an engaging philosophical video that makes viewers think deeply about life, meaning, or values while keeping the content accessible, practical, and visually compelling. Focus on how philosophical ideas can improve viewers' lives or change their perspective in meaningful ways.
        ''',
        "life pro tips": '''
        Guidelines for creating the life pro tip:
        1. Practicality: Ensure the tip is applicable to everyday situations within the chosen category.
        2. Clarity: Present the tip in clear, concise language.
        3. Uniqueness: Provide a tip that is not commonly known or offers a new perspective in the chosen category.
        4. Actionable advice: Give specific steps or methods to implement the tip.
        5. Impact: Focus on a tip that can make a significant difference in people's lives within the chosen category.
        6. Explanation: Provide a detailed explanation of why the tip works and how it can be beneficial.
        7. Examples: Include one or two examples of how the tip can be applied in real-life situations.
        ''',
        "fun facts": '''
        Guidelines for creating the fun fact:
        1. Topic: Choose an interesting subject from areas such as science, history, culture, nature, or technology.
        2. Surprise factor: Focus on a fact that is likely to surprise or amaze the reader.
        3. Relevance: Select a fact that has some connection to everyday life or current events.
        4. Clarity: Present the fact in clear, concise language that's easy to understand.
        5. Depth: Provide a detailed explanation or additional context for the fact.
        6. Engagement: Frame the fact in a way that sparks curiosity and encourages further exploration.
        7. Accuracy: Ensure the fact is true and verifiable.
        ''',
        "long form jokes": '''
        Guidelines for creating the long form joke:
        1. Narrative structure: Compose a lengthy joke that incorporates an engaging narrative structure as a build-up, followed by a clever and uproarious punchline.
        2. Detailed storyline: Craft a well-developed and detailed storyline that leads up to the punchline, ensuring it is both witty and side-splittingly funny.
        3. Character development: Introduce one or more interesting characters and develop them throughout the joke.
        4. Setting: Establish a clear and vivid setting that enhances the humor of the situation.
        5. Pacing: Maintain a good rhythm throughout the joke, building tension and anticipation as you approach the punchline.
        6. Misdirection: Use clever misdirection to set up unexpected twists or surprises.
        7. Callbacks: If possible, incorporate callbacks to earlier elements of the joke for added humor.
        8. Punchline: Deliver a strong, unexpected, and hilarious punchline that ties everything together.
        9. Appropriate content: Ensure the joke is suitable for a general audience, avoiding offensive or overly controversial topics.
        ''',
        "bedtime": '''
        1. Main character: Introduce a relatable protagonist that children can easily connect with.
        2. Setting: Establish a cozy, magical, or dreamy setting appropriate for a bedtime story.
        3. Gentle plot: Present a mild adventure or problem that is resolved peacefully.
        4. Emotional journey: Develop the character's emotions in a way that promotes feelings of comfort and security.
        5. Story structure: Follow a clear beginning, middle, and end, with a calming resolution.
        6. Language and style: Use simple, soothing language suitable for children, with some repetition or rhythmic elements.
        7. Moral or lesson: Include a subtle, positive message or gentle life lesson.

        Ensure the story is calming and conducive to sleep, avoiding any scary elements or overly exciting adventures.
        The story should help children wind down and prepare for sleep.
        ''',
        "urban legends": '''
        1. Setting: Establish a specific region or culture where the legend takes place.
        2. Origins: Describe the supposed origins of the legend, including any historical context.
        3. Main narrative: Tell the core story of the urban legend in vivid detail.
        4. Variations: Mention any notable variations of the legend, if applicable.
        5. Real events: If relevant, reference any real events that might have inspired the legend.
        6. Atmospheric elements: Include details that create a sense of unease or fear.
        7. Cultural impact: Briefly discuss how the legend has affected local culture or beliefs.
        8. Ambiguity: Maintain some level of uncertainty about the truth of the legend.

        Tell the story of an urban legend from a specific region or culture. Describe the legend in detail, including its origins, variations, and any real events that might have inspired it. Your goal is to create a chilling and captivating narrative that draws viewers into the world of urban folklore.
        ''',
        "aita stories": '''
        1. Conflict setup: Establish a clear moral dilemma or interpersonal conflict.
        2. First-person perspective: Write in first person as if you're the person asking for judgment.
        3. Context: Provide relevant background information that led to the conflict.
        4. Actions taken: Describe what you did that might be considered right or wrong.
        5. Other perspectives: Include how other people involved reacted or felt.
        6. Self-doubt: Express uncertainty about whether your actions were justified.
        7. Emotional impact: Include your feelings and the emotional impact of the situation.
        8. Question: End with a clear question about whether you were in the wrong.

        Create a compelling AITA (Am I The Asshole) story that presents a morally ambiguous situation where reasonable people might disagree about who was wrong. The story should be engaging, realistic, and involve a relatable everyday conflict that viewers can form opinions about.
        ''',
        "storytime": '''
        1. Hook: Begin with an attention-grabbing statement or question to immediately engage viewers.
        2. Personal experience: Frame the story as a first-person account of something that happened to you or someone close.
        3. Drama: Include elements of surprise, conflict, or unusual circumstances to maintain interest.
        4. Details: Provide vivid, specific details that make the story feel authentic and immersive.
        5. Pacing: Build tension by revealing information gradually, saving key revelations for later in the story.
        6. Emotional journey: Express your feelings throughout different parts of the narrative.
        7. Resolution: Conclude with how the situation was resolved and what you learned.
        8. Authenticity: Maintain a conversational, authentic tone throughout the story.

        Create an engaging "Storytime" narrative that feels like a friend sharing an incredible, surprising, or shocking personal experience. Focus on creating moments of tension, surprise, and emotional impact to keep viewers invested until the end.
        ''',
        "pov": '''
        1. Character perspective: Establish a clear and specific point of view character.
        2. Immersive scenario: Create a detailed scenario that viewers can imagine themselves in.
        3. Sensory details: Include specific sights, sounds, feelings, and thoughts from this perspective.
        4. Direct address: Use "you" statements to place the viewer directly in the scenario.
        5. Emotional impact: Focus on the emotions and reactions one would feel in this situation.
        6. Narrative progression: Create a mini-story with a beginning, middle, and end.
        7. Authenticity: Make the experience feel realistic and relatable, even in fantastical scenarios.
        8. Engagement: Include moments of tension, discovery, or revelation to maintain interest.

        Create an immersive "POV" (Point of View) experience that transports viewers into a specific scenario, situation, or character's perspective. The content should feel immersive and allow viewers to imagine themselves directly experiencing the described situation.
        ''',
        "day in the life": '''
        1. Time progression: Structure the content chronologically from morning to evening.
        2. Routine details: Highlight specific, interesting elements of daily routines.
        3. Unique aspects: Focus on what makes this particular lifestyle or daily routine special or different.
        4. Behind-the-scenes: Include lesser-known aspects that people might not expect.
        5. Challenges: Address difficulties or challenges faced during a typical day.
        6. Rewards: Highlight enjoyable or rewarding moments in the day.
        7. Personal touch: Include thoughts, feelings, and personality to make it relatable.
        8. Authenticity: Maintain a realistic portrayal without excessive glamorization.

        Create an engaging "Day in the Life" narrative that follows someone through their typical day, offering insight into a particular lifestyle, profession, or routine. The content should feel authentic while highlighting interesting aspects that viewers might find surprising or compelling.
        ''',
        "true crime": '''
        1. Case overview: Begin with a concise summary of the crime, including when and where it occurred.
        2. Victim(s): Provide respectful information about the victim(s), humanizing them beyond their victimhood.
        3. Timeline: Present a clear chronology of events leading up to, during, and after the crime.
        4. Investigation: Detail the key elements of how the case was investigated.
        5. Evidence: Discuss significant evidence that helped solve (or complicate) the case.
        6. Perpetrator: Provide relevant information about the perpetrator and their motives.
        7. Justice: Explain the outcome of the case, including any trial, conviction, and sentencing.
        8. Impact: Discuss the broader impact of the case on the community, laws, or investigation techniques.
        9. Sensitivity: Maintain a respectful tone that avoids sensationalism or glorification of the crime.

        Create a factual, informative true crime narrative about a real criminal case. The content should be educational and thought-provoking while maintaining respect for victims and avoiding glorification of criminal actions. Focus on the investigative process, evidence, and impact rather than gruesome details.
        ''',
        "celebrity facts": '''
        1. Subject selection: Choose a well-known celebrity with interesting aspects to their life or career.
        2. Lesser-known information: Focus on facts that aren't commonly known by the general public.
        3. Career highlights: Include notable achievements or turning points in their career.
        4. Personal insights: Share intriguing details about their personal life, habits, or interests.
        5. Background story: Include interesting elements of their upbringing or journey to fame.
        6. Connections: Mention surprising relationships or connections to other celebrities or events.
        7. Transformations: Highlight significant changes in their career, appearance, or public persona.
        8. Legacy: Discuss their influence or impact on their industry or popular culture.
        9. Accuracy: Ensure all information is factual and properly researched.

        Create an engaging collection of lesser-known facts about a celebrity, focusing on surprising, interesting, or thought-provoking information that gives deeper insight into the person beyond their public image. Avoid common knowledge and widely reported aspects of their life.
        ''',
        "conspiracy theories": '''
        1. Theory introduction: Clearly present the conspiracy theory without immediately endorsing or dismissing it.
        2. Origins: Explain where and when the theory originated and how it has evolved.
        3. Key claims: Break down the main assertions of the conspiracy theory.
        4. Supporting evidence: Present the evidence that proponents use to support the theory.
        5. Counter-evidence: Include information that challenges or refutes the theory.
        6. Cultural impact: Discuss how the theory has influenced media, politics, or society.
        7. Expert perspectives: Include what relevant experts say about the theory.
        8. Critical thinking: Encourage viewers to evaluate the evidence and think critically.
        9. Educational approach: Maintain an informative rather than sensationalistic tone.

        Create an educational exploration of a conspiracy theory that examines its claims, evidence, and counter-evidence in a balanced way. The content should analyze the theory from multiple perspectives while encouraging critical thinking. Avoid presenting unverified claims as facts or dismissing theories without examining the evidence.
        ''',
        "money saving tips": '''
        1. Problem identification: Identify a specific financial challenge or expense category.
        2. Practical solutions: Provide concrete, actionable strategies to save money in this area.
        3. Immediate implementation: Focus on tips that can be applied right away without significant preparation.
        4. Quantifiable savings: Include specific amounts or percentages that can potentially be saved.
        5. Accessibility: Ensure tips are applicable for people with average incomes and resources.
        6. Explanation: Clarify why and how each tip works to save money.
        7. Common mistakes: Point out frequent errors people make that cost them money in this area.
        8. Examples: Include real-world examples or scenarios showing the tips in action.
        9. Long-term impact: Explain how implementing these tips can affect long-term financial health.

        Create practical, actionable money-saving tips that viewers can implement immediately to reduce expenses and improve their financial situation. Focus on specific strategies rather than general advice, and ensure the tips provide meaningful savings without requiring significant lifestyle changes.
        ''',
        "fitness hacks": '''
        1. Specific goal: Target a particular fitness objective (e.g., building muscle, increasing endurance, improving flexibility).
        2. Efficiency: Focus on techniques that save time or effort while maintaining effectiveness.
        3. Accessibility: Ensure tips can be implemented without expensive equipment or special facilities.
        4. Scientific basis: Include the physiological reasoning behind why the hack works.
        5. Common obstacles: Address typical challenges people face related to this fitness goal.
        6. Proper technique: Emphasize correct form to prevent injury and maximize results.
        7. Progressive application: Explain how beginners can start and how advanced fitness enthusiasts can modify.
        8. Results timeframe: Set realistic expectations for when results might be noticeable.
        9. Complementary approaches: Suggest related nutrition or recovery strategies that enhance the fitness hack.

        Create practical, science-based fitness hacks that help viewers achieve better results with less time, effort, or equipment. Focus on innovative techniques or approaches that aren't widely known but are effective and accessible for most people, regardless of their fitness level.
        ''',
        "psychology facts": '''
        1. Phenomenon selection: Choose a specific psychological phenomenon, bias, or behavior pattern.
        2. Scientific explanation: Provide a clear, accessible explanation of the psychology behind it.
        3. Real-world examples: Include everyday situations where this psychological principle manifests.
        4. Research background: Mention key studies or experiments that discovered or validated this phenomenon.
        5. Self-recognition: Help viewers identify this pattern in their own thoughts or behaviors.
        6. Practical application: Offer ways to use this knowledge to improve decision-making or relationships.
        7. Common misconceptions: Address widespread misunderstandings about this aspect of psychology.
        8. Neurological basis: When relevant, briefly explain the brain mechanisms involved.
        9. Ethical considerations: Discuss how this knowledge can be used responsibly and ethically.

        Create an educational explanation of a fascinating psychological fact, principle, or phenomenon that helps viewers better understand human behavior, thinking, or emotion. The content should be scientifically accurate while remaining accessible and relevant to everyday life.
        ''',
        "product reviews": '''
        1. Product introduction: Clearly identify the product, including brand, model, and key specifications.
        2. First impressions: Share initial reactions to the packaging, design, and out-of-box experience.
        3. Testing methodology: Explain how you tested the product under real-world conditions.
        4. Key features: Highlight the most important features and how well they perform.
        5. Pros and cons: Provide a balanced assessment of strengths and weaknesses.
        6. Comparisons: Briefly compare with similar products in the same category or price range.
        7. Value assessment: Discuss whether the product provides good value for its price.
        8. Ideal user: Identify who would benefit most from this product.
        9. Final verdict: Conclude with a clear recommendation and overall rating.

        Create an honest, thorough product review that helps viewers make an informed purchasing decision. The review should be based on actual experience with the product and provide a balanced perspective that highlights both positives and negatives without bias.
        ''',
        "travel guides": '''
        1. Destination overview: Introduce the location, including its unique appeal and character.
        2. Best time to visit: Recommend optimal seasons or months considering weather, crowds, and events.
        3. Must-see attractions: Highlight essential experiences and landmarks, including lesser-known spots.
        4. Local cuisine: Recommend specific foods, dishes, and places to eat for an authentic experience.
        5. Transportation tips: Provide practical advice on getting around efficiently and affordably.
        6. Accommodation recommendations: Suggest neighborhoods or specific places to stay for different budgets.
        7. Cultural insights: Offer information about local customs, etiquette, or phrases to know.
        8. Budget considerations: Include approximate costs and money-saving tips for the destination.
        9. Practical advice: Share insights on safety, packing suggestions, or common tourist mistakes to avoid.

        Create an informative travel guide that provides practical, specific information to help viewers plan a trip to a particular destination. The content should combine essential tourist information with insider tips that enhance the travel experience beyond the typical guidebook recommendations.
        ''',
        "diy tutorials": '''
        1. Project scope: Clearly define what will be created and its purpose or benefits.
        2. Materials list: Provide a complete list of required supplies, tools, and their alternatives.
        3. Skill level: Indicate the appropriate skill level and any techniques viewers should be familiar with.
        4. Step-by-step process: Break down the project into clear, sequential steps.
        5. Technique demonstration: Explain specific techniques with enough detail for viewers to replicate.
        6. Common mistakes: Highlight potential pitfalls and how to avoid or fix them.
        7. Customization options: Suggest variations or personalization possibilities.
        8. Time estimate: Provide a realistic timeframe for completing the project.
        9. Final result: Describe what the successful completed project should look like or how it should function.

        Create a detailed DIY tutorial that teaches viewers how to make or build something specific from start to finish. The tutorial should be accessible to the appropriate skill level while providing enough detail that viewers can successfully complete the project by following the instructions.
        ''',
        "cooking tips": '''
        1. Technique focus: Concentrate on a specific cooking method, ingredient preparation, or kitchen skill.
        2. Problem-solving: Address common cooking challenges or mistakes related to this technique.
        3. Science explanation: Briefly explain the food science behind why the technique works.
        4. Tool recommendations: Suggest appropriate utensils or equipment that help execute the technique.
        5. Ingredient selection: Provide guidance on choosing the right ingredients for best results.
        6. Visual indicators: Describe what to look for to know the technique is being performed correctly.
        7. Adaptability: Explain how the technique can be applied to different recipes or ingredients.
        8. Time-saving aspects: Highlight how mastering this technique can improve efficiency in the kitchen.
        9. Flavor impact: Describe how the technique affects taste, texture, or presentation of food.

        Create practical cooking tips that help viewers improve their culinary skills, focusing on techniques rather than specific recipes. The content should enhance food preparation knowledge with professional insights that can be applied broadly to improve cooking results.
        ''',
        "dating advice": '''
        1. Specific scenario: Focus on a particular dating stage or common challenge.
        2. Psychology insights: Include relevant psychological principles that explain dating behaviors.
        3. Practical strategies: Provide actionable advice that can be implemented immediately.
        4. Communication guidance: Offer tips for effective expression and listening in dating contexts.
        5. Red flags and green flags: Help viewers identify both concerning and promising signs.
        6. Self-reflection: Encourage introspection about personal patterns, needs, and goals.
        7. Respectful approach: Emphasize consent, boundaries, and treating others with dignity.
        8. Realistic expectations: Address common misconceptions about dating and relationships.
        9. Success indicators: Explain how to recognize when advice is working or when to try a different approach.

        Create helpful dating advice that assists viewers in navigating romantic relationships more successfully. The content should combine psychological insights with practical strategies while promoting healthy, respectful approaches to dating and relationships.
        ''',
        "motivational": '''
        1. Inspiring message: Craft a powerful, uplifting message that motivates viewers to take action.
        2. Personal transformation: Share a compelling story of overcoming obstacles or achieving significant growth.
        3. Actionable takeaways: Include practical steps viewers can implement in their own lives.
        4. Emotional resonance: Create content that connects emotionally and inspires genuine feelings of motivation.
        5. Universal themes: Focus on themes like perseverance, resilience, growth, and self-belief that resonate widely.
        6. Authenticity: Ensure the motivational message feels genuine and not overly clichéd.
        7. Call to action: End with a clear, inspiring call that encourages viewers to make positive changes.

        Create an inspiring motivational piece that genuinely uplifts viewers and provides them with both emotional inspiration and practical guidance for personal growth or achievement.
        ''',
        "scary": '''
        1. Atmosphere: Create a tense, eerie, or unsettling atmosphere through descriptive language.
        2. Suspense building: Gradually build tension and dread throughout the narrative.
        3. Fear triggers: Incorporate universal fear triggers or phobias that resonate with many viewers.
        4. Psychological elements: Include psychological horror elements that play on deeper fears.
        5. Unexpected twists: Include surprising developments or revelations that heighten the fear factor.
        6. Sensory details: Use vivid sensory details to make the frightening elements feel more immediate.
        7. Lingering impact: Craft a story that leaves viewers with a lasting sense of unease or fear.

        Create a genuinely frightening story that builds suspense and creates a sense of dread or fear in viewers through atmospheric storytelling and psychological tension.
        ''',
        "mystery": '''
        1. Central puzzle: Present an intriguing mystery or puzzle that needs to be solved.
        2. Clues and red herrings: Scatter meaningful clues alongside misleading information.
        3. Suspense: Build tension and curiosity that keeps viewers engaged and guessing.
        4. Character secrets: Include characters with hidden motives, backgrounds, or knowledge.
        5. Revelations: Structure the narrative to include surprising but logical revelations.
        6. Resolution: Provide a satisfying explanation that makes sense given the established clues.
        7. Lingering questions: Optionally, leave some smaller mysteries unresolved to maintain intrigue.

        Create an engaging mystery that presents viewers with an intriguing puzzle, provides subtle clues throughout, and delivers a satisfying resolution that makes sense in retrospect.
        ''',
        "bedtime": '''
        1. Gentle tone: Use soothing, calming language appropriate for bedtime listening.
        2. Imaginative elements: Include whimsical or fantastical elements that inspire pleasant dreams.
        3. Positive themes: Focus on comforting, reassuring themes that promote feelings of security.
        4. Simple structure: Use a straightforward narrative structure that's easy to follow when sleepy.
        5. Rhythmic language: Incorporate gentle rhythm and repetition that has a lulling quality.
        6. Peaceful resolution: End with a satisfying, peaceful conclusion that leaves listeners content.
        7. Sensory comfort: Include descriptions of cozy, comfortable settings and situations.

        Create a soothing bedtime story with a gentle tone, comforting themes, and a peaceful resolution that helps listeners relax and prepare for sleep.
        ''',
        "pet tips": '''
        1. Pet care advice: Provide practical, actionable tips for pet owners to improve their pet's health, behavior, or quality of life.
        2. Expert insights: Include veterinary or animal behavior knowledge that pet owners might not know.
        3. Common problems: Address frequent challenges pet owners face with solutions that work.
        4. Safety information: Highlight potential hazards or warning signs pet owners should be aware of.
        5. Species-specific guidance: Tailor advice to specific types of pets (dogs, cats, birds, etc.) when relevant.
        6. Enrichment ideas: Suggest activities, toys, or interactions that improve pet mental and physical wellbeing.
        7. Bonding techniques: Include ways to strengthen the human-animal relationship.
        8. Cost-effective solutions: Offer affordable alternatives to expensive pet products when possible.

        Create engaging, helpful content for pet owners that combines practical advice with heartwarming elements that celebrate the human-animal bond. Content can include pet care tips, training advice, health information, or heartwarming pet stories that resonate with animal lovers.
        ''',
        "islamic": '''
        1. Hadith selection: Choose an authentic hadith (saying of Prophet Muhammad) that contains valuable wisdom or guidance.
        2. Source citation: Mention the authentic collection the hadith comes from (e.g., Sahih Bukhari, Sahih Muslim).
        3. Context: Provide historical or situational context that helps understand the hadith's significance.
        4. Explanation: Offer a clear, accessible explanation of the hadith's meaning and teachings.
        5. Modern relevance: Connect the hadith's wisdom to contemporary life situations and challenges.
        6. Practical application: Suggest ways viewers can apply this teaching in their daily lives.
        7. Ethical principles: Highlight the moral or ethical principles embodied in the teaching.
        8. Respectful tone: Maintain a respectful, educational approach that honors the religious tradition.
        9. Universal values: When appropriate, emphasize universal values that can resonate with people of all backgrounds.

        Create educational, inspiring content based on authentic Islamic teachings that provides valuable wisdom, historical insights, or ethical guidance. Focus on making the content accessible and relevant to contemporary life while maintaining accuracy and respect for the tradition.
        ''',
    }

    # Default guidelines for unspecified story types
    default_guidelines = '''
    1. Main character: Introduce a compelling protagonist with clear goals or desires.
    2. Setting: Establish a vivid and appropriate setting for the story.
    3. Conflict: Present a central conflict or challenge for the main character to overcome.
    4. Emotional journey: Develop the character's emotions throughout the story.
    5. Story structure: Follow a clear beginning (setup), middle (confrontation), and end (resolution).
    6. Language and style: Adapt the language to suit the {story_type}.

    Ensure the story captures the reader's attention and imagination while staying true to the conventions of {story_type}.
    Be creative and surprising in your storytelling approach within the boundaries of the chosen story type.
    '''

    guidelines = story_type_guidelines.get(story_type.lower(), default_guidelines)

    # Special case for 'Interesting History'
    if story_type.lower() == 'interesting history':
        guidelines += '''
        Important: Ensure the story is based on real historical events and figures.
        Provide accurate historical details while maintaining an engaging narrative.
        '''

    return guidelines.format(story_type=story_type)

def create_story_prompt(story_type: str, char_limit: Tuple[int, int]) -> str:
    base_prompt = f'''
    Create content based on the following guidelines:

    1. Title: Create an engaging and relevant title. Ensure they are unique and avoid repetition.
    2. Description: Write a 100-150 character description that provides an overview of the content. Include 2-3 relevant hashtags, followed by #facelessvideos.app as the last hashtag.
    3. Content: Generate the main content according to the specific guidelines below.

    {get_story_type_guidelines(story_type)}

    Important: Please ensure the total character count is between {char_limit[0]} and {char_limit[1]} characters.

    Format your response as follows:
    Title: [Your generated title]

    Description: [Your generated description]

    [Your generated content]
    '''
    return base_prompt


def generate_story_and_title(client, story_type: str, custom_title=None) -> Tuple[str, str, str]:
    config = load_config()
    char_limit = (config['story_generation']['char_limit_min'], config['story_generation']['char_limit_max'])

    # Create the prompt based on whether we have a custom title or not
    if custom_title:
        # Modify the prompt to use the provided title
        prompt = f'''
        Create content based on the following guidelines:

        1. Title: Use the provided title: "{custom_title}"
        2. Description: Write a 100-150 character description that provides an overview of the content. Include 2-3 relevant hashtags, followed by #facelessvideos.app as the last hashtag.
        3. Content: Generate the main content according to the specific guidelines below.
        4. IMPORTANT: The content must be relevant to the provided title: "{custom_title}"

        {get_story_type_guidelines(story_type)}

        Important: Please ensure the total character count is between {char_limit[0]} and {char_limit[1]} characters.

        Format your response as follows:
        Title: {custom_title}

        Description: [Your generated description]

        [Your generated content]
        '''
    else:
        # Add randomization to prevent repetition in bulk generation
        import random

        # Generate a random seed for each story to ensure variety
        random_seed = random.randint(1, 10000)

        # Create a list of potential themes or angles for this story type
        themes = [
            "focus on an unexpected twist",
            "include a surprising revelation",
            "explore a controversial perspective",
            "highlight a lesser-known aspect",
            "present a unique historical context",
            "incorporate a modern perspective",
            "challenge a common misconception",
            "present a personal transformation story",
            "focus on practical applications",
            "explore cultural differences"
        ]

        # Select a random theme
        selected_theme = random.choice(themes)

        # Use the standard prompt with added randomization
        base_prompt = create_story_prompt(story_type, char_limit)
        prompt = f'''
        {base_prompt}

        To make this content unique and avoid repetition:
        - Use random seed #{random_seed} for inspiration
        - {selected_theme}
        - Create content that is distinctly different from other stories of this type
        - Avoid common or overused narratives for this topic
        '''

    messages = [
        {
            "role": "system",
            "content": '''You are an expert content creator specialized in viral short-form videos for TikTok, Instagram Reels and YouTube Shorts. Your content is known for:

            1. Highly engaging hooks that grab attention in the first 3 seconds
            2. Clear, concise, and conversational storytelling optimized for short attention spans
            3. Strategic pacing with unexpected twists, revelations, or emotional moments that keep viewers watching
            4. Relatable, authentic content that resonates with specific audience interests
            5. Use of proven engagement patterns like "Wait for it..." or setting up anticipation
            6. Content that's designed to be shareable, commentable, and spark discussion
            7. Strong emotional triggers - humor, surprise, inspiration, or controversy
            8. Use simple and modern English so even 7th graders can understand it
            9. Use only standard ASCII characters (no special quotes, em dashes, etc.)
            10. Avoid quotes within quotes in titles to prevent JSON parsing issues

            When creating titles:
            - Make them attention-grabbing with specific, intriguing details
            - Use power words that evoke emotion or curiosity
            - Include numbers or specificity that promises valuable information
            - Consider SEO and search-friendly phrasing while maintaining appeal
            - Create a sense of urgency or must-know information
            - Ensure each title is unique and not repetitive of other content

            When creating descriptions:
            - Include relevant trending hashtags that will maximize discovery
            - Use emoji strategically to enhance emotional impact
            - Create a clear content premise that promises value or entertainment
            - Incorporate call-to-action phrases that encourage engagement

            Most importantly, your content is optimized for the fast-paced, visually-driven format of short-form video, with clear story arcs that work well with captions and deliver on viewer expectations while maintaining their attention throughout.'''
        },
        {"role": "user", "content": prompt},
    ]

    response = call_openai_api(client, messages)
    if response:
        parts = response.split("\n\n", 2)
        if len(parts) == 3:
            # If we have a custom title, use it instead of the generated one
            if custom_title:
                title = custom_title
            else:
                title = parts[0].replace("Title: ", "").strip()

            description = parts[1].replace("Description: ", "").strip()
            content = parts[2].strip()

            # Ensure the first hashtag is from the STORY_TYPE_HASHTAGS mapping
            first_hashtag = STORY_TYPE_HASHTAGS[story_type]

            # Split the description into text and hashtags
            desc_parts = description.split('#', 1)
            if len(desc_parts) > 1:
                desc_text = desc_parts[0].strip()
                existing_hashtags = desc_parts[1].strip()
                # Remove #facelessvideos.app if it's present in existing_hashtags
                existing_hashtags = existing_hashtags.replace("facelessvideos.app", "").strip()
                # Combine hashtags, ensuring the mapped hashtag is first
                all_hashtags = f"{first_hashtag} #{existing_hashtags} #facelessvideos.app"
                description = f"{desc_text} {all_hashtags.strip()}"
            else:
                # If no hashtags were present, add the mapped hashtag and #facelessvideos.app
                description += f" {first_hashtag} #facelessvideos.app"

            return title, description, content
    return None, None, None

def generate_characters(client, story: str) -> List[Dict[str, str]]:
    prompt = f"""Based on the following story, create detailed descriptions for each character, including their name, ethnicity, gender, age, facial features, body type, hair style, and accessories. Focus on permanent or long-term attributes.

        Story:
        {story}

        Output format:
        [
            {{
                "name": "Character Name",
                "ethnicity": "Character's Ethnicity",
                "gender": "Character's Gender",
                "age": "Character's Age",
                "facial_features": "Description of Character's facial features",
                "body_type": "Description of Character's body type",
                "hair_style": "Description of Character's hair style",
                "accessories": "Description of Character's accessories"
            }},
            ...
        ]

        Guidelines:
        - Include the character's name as it appears in the story.
        - Specify the character's ethnicity if it's relevant and discernible from the story.
        - State the character's gender.
        - Specify the character's age or apparent age range.
        - For facial features, include details about eyes, nose, mouth, chin, forehead, cheekbones, and overall face shape. Include any notable unique features like scars, birthmarks, or facial hair.
        - Describe the character's body type, including height and build.
        - For hair style, describe the color, length, style, and texture.
        - For accessories, include only non-clothing items such as jewelry, glasses and watches that are consistently associated with the character.
        - Aim for concise but descriptive entries for each attribute.
        - Focus on permanent or long-term features, not on changeable expressions or temporary states.
        - Do not include any descriptions of clothing or attire.

        Please provide only the JSON array, without any additional text.
        """

    messages = [
        {
            "role": "system",
            "content": """You are an expert at analyzing stories and creating detailed, vivid character descriptions, focusing on overall appearance. Your skills include:
                1. Extracting subtle character details from narrative context
                2. Creating consistent and believable descriptions of characters
                3. Focusing on permanent features and distinguishing attributes
                4. Adapting descriptions to fit the story's genre and tone
                5. Balancing physical features with character essence
                6. Translating character personalities into comprehensive physical attributes
                7. Accurately estimating and describing characters' attributes based on story context
                8. Avoiding any mention of clothing or attire in character descriptions"""
        },
        {"role": "user", "content": prompt},
    ]

    response = call_openai_api(client, messages)
    if not response:
        print("API returned empty response")

    try:
        return json.loads(response)
    except json.JSONDecodeError:
        # if the direct parsing fails, try to extract the JSON array part
        array_match = re.search(r'\[.*\]', response, re.DOTALL)
        if array_match:
            try:
                return json.loads(array_match.group())
            except json.JSONDecodeError:
                print("Failed to parse the response as a JSON array.")
                return []
        else:
            print("No JSON array found in the response.")
            return []


def generate_storyboard(client, title: str, story: str, story_type: str, character_names: List[str] = None) -> Dict[str, Any]:
    config = load_config()
    max_scenes = config['storyboard']['max_scenes']
    timestamp = datetime.now().strftime("%Y-%m-%d %I:%M:%S %p")

    # define type-specific guidelines
    type_guidelines = {
        "general": "Focus on creating cinematic, visually rich scenes that capture key moments with strong composition, dramatic lighting, and vivid details. Describe the environment, mood, color palette, and visual focal points.",
        "philosophy": "Create visually compelling scenes that represent philosophical concepts through relatable, everyday situations and concrete imagery. Use lighting, composition, and color to convey meaning while keeping the content accessible and practical. Focus on how philosophical ideas apply to real life.",
        "fun facts": "Design visually informative scenes that illustrate the key information through clear visual metaphors, diagrams, or representations. Use a clean, engaging visual style with educational elements that explain the concept visually.",
        "life pro tips": "Create practical demonstration scenes showing the tip in action with clear before/after comparisons. Include visual details that highlight the process, tools, and results with step-by-step visual clarity.",
        "aita stories": "Design emotionally charged scenes with strong character expressions, body language, and environmental details that reveal the interpersonal conflict. Use composition to show power dynamics and relationships between characters.",
        "storytime": "Create immersive, detailed scenes with rich environmental storytelling that places viewers in the moment. Use visual elements that build narrative tension and highlight emotional high points.",
        "pov": "Design scenes from a first-person perspective with immersive details that put viewers directly in the experience. Include visual elements that create a sense of presence and immediacy.",
        "day in the life": "Create scenes that follow a chronological flow with distinct visual changes in lighting, setting, and activities throughout the day. Include authentic details that characterize the lifestyle being portrayed.",
        "true crime": "Design factual, documentary-style scenes with evidence visualization, location reconstructions, and timeline elements. Use a realistic visual approach that respects the serious nature of the content.",
        "celebrity facts": "Create visually interesting scenes that reveal new perspectives on the celebrity through unique visual angles, behind-the-scenes elements, or revealing moments that showcase lesser-known aspects.",
        "conspiracy theories": "Design scenes that visualize complex connections, evidence, and theories through diagrams, visual metaphors, and atmospheric imagery that creates intrigue without presenting speculation as fact.",
        "money saving tips": "Create clear instructional scenes with practical visual demonstrations, comparisons, and results of applying the financial advice. Use visual elements that quantify savings and show processes.",
        "fitness hacks": "Design dynamic scenes showing exercise techniques with clear form visualization, body mechanics, and progression. Include before/after elements and visual cues for proper technique.",
        "psychology facts": "Create visually explanatory scenes that illustrate psychological concepts through demonstrative scenarios, brain visualization, or behavior models that make abstract concepts concrete.",
        "product reviews": "Design detailed product showcase scenes with close-up details, usage demonstrations, and comparison elements. Include visual highlights of key features and performance aspects.",
        "travel guides": "Create visually transportive scenes that showcase the destination's beauty, culture, and unique features. Include diverse visual settings that highlight both iconic and hidden gems.",
        "cooking tips": "Design instructional cooking scenes with clear technique visualization, ingredient close-ups, and process details. Include before/after visuals and key technique moments.",
        "dating advice": "Create relatable dating scenario scenes with expressive character interactions, environmental cues, and body language details. Show visual contrasts between effective and ineffective approaches.",
        "scary": "Design atmospheric horror scenes with strategic shadows, unsettling compositions, and suspense-building visual elements. Use color, lighting, and framing to create dread and tension.",
        "mystery": "Create intriguing scenes with visual clues, atmospheric settings, and suspense-building elements. Use lighting and composition to highlight key details while maintaining a sense of the unknown.",
    }

    # define type-specific opening scene instructions
    opening_scene_instructions = {
        "general": "A visually striking, cinematically composed scene (70-80 words) that introduces the main theme through powerful imagery. Include specific visual details about lighting, color palette, environment, and focal elements that establish the mood and hook viewers with an intriguing visual question.",
        "philosophy": "A thought-provoking visual scene (70-80 words) that presents the philosophical question through a relatable, everyday situation. Include specific visual details about the setting, character expressions, and environmental elements that make the philosophical concept accessible and immediately engaging to viewers.",
        "fun facts": "A visually surprising scene (70-80 words) that introduces the topic through an unexpected or eye-catching visual. Include specific details about how the information is visually represented, with educational elements that immediately spark curiosity about the fact to be revealed.",
        "life pro tips": "A visually relatable scene (70-80 words) showing a common problem or frustration that the tip will solve. Include specific visual details of the struggle, environmental elements, and character reactions that establish the need for the solution you'll present.",
        "aita stories": "A visually tense scene (70-80 words) that establishes the moral conflict through environmental and character details. Include specific visual cues about the setting, character expressions, and situational elements that immediately draw viewers into the dilemma.",
        "storytime": "A visually captivating hook scene (70-80 words) that introduces the story's most intriguing element. Include specific visual details about the setting, character reactions, and unexpected elements that create immediate curiosity and emotional investment.",
        "pov": "An immersive first-person visual scene (70-80 words) that puts viewers directly in a compelling situation. Include specific visual details about what you see from this perspective, with environmental and sensory elements that create immediate presence.",
    }

    # define type-specific additional guidelines
    additional_guidelines = {
        "general": """
        - Describe characters' clothing in detail, ensuring consistency within scenes.
        - Cover the entire story without omitting any significant parts.
        - Use the provided character full names in the descriptions.
        """,
        "philosophy": """
        - Include scenes that show characters applying philosophical concepts in everyday situations.
        - Focus on practical wisdom and relatable examples rather than abstract theory.
        - Show characters experiencing realizations or "aha moments" that viewers can relate to.
        - Use simple visual metaphors that make philosophical ideas easy to understand.
        - Ensure subtitles are plain text without formatting symbols like asterisks or columns/bars.
        - Avoid using the misspelled term "philospy" - always use "philosophy" correctly.
        """,
        "fun facts": """
        - Use visual metaphors or analogies to help explain complex ideas if necessary.
        - Include scenes that show the real-world application or implications of the fun fact, if applicable.
        - Ensure subtitles are plain text without formatting symbols like asterisks.
        """,
        "life pro tips": """
        - Use before-and-after style scenes to show the impact of applying the tip, if applicable.
        - Include scenes that show both the process of implementing the tip and its positive outcomes.
        - Ensure subtitles are plain text without formatting symbols.
        """,
    }

    prompt = f"""Based on the following {story_type}, create a visually detailed storyboard with up to {max_scenes} scenes optimized for captivating short-form videos.

        Title: {title}
        {"Character full Names: " + ', '.join(character_names) if character_names else ""}

        First, create an opening scene that immediately hooks viewers:
        1. Scene Number: 1
        2. Description: {opening_scene_instructions.get(story_type, opening_scene_instructions["general"])} Include specific camera angles, lighting, and composition details that would make this scene visually compelling in a short video format.
        3. Subtitles: An engaging question or statement that captures the essence of the {story_type} and immediately grabs the viewer's attention.

        Then, for each subsequent scene, provide the following visual details:
        1. Scene Number
        2. Description: A highly visual description (80-100 words) focusing on cinematic elements including:
           - Specific camera angles (close-up, medium shot, wide shot, overhead, etc.)
           - Lighting quality and direction (harsh, soft, backlit, silhouette, etc.)
           - Color palette and mood
           - Key visual elements and composition
           - Environment and setting details
           - Any motion, transitions, or visual effects
           - Character positioning, expressions, and body language if applicable
           {type_guidelines.get(story_type, type_guidelines["general"])}
        3. Subtitles: Use EXACT quotes from the original text that perfectly match the visual elements described.
        4. Transition: Specify the type of transition to the current scene, explaining how it enhances the visual storytelling.

        Guidelines:
        - Create scenes specifically optimized for vertical short-form video content (TikTok, Instagram Reels, YouTube Shorts)
        - Focus on highly visual, attention-grabbing elements that work well in the first 3 seconds to hook viewers
        - Design each scene to be visually compelling even with the sound off
        - Ensure subtitles MUST contain only exact text from the original text, without any additions, omissions, or modifications
        - Include every sentence from the original text in the subtitles, maintaining the correct order across all scenes
        - Each subtitle must be unique; do not repeat content in multiple scenes
        - For partial sentences at scene boundaries, include the fragment and continue it in the next scene's subtitles
        - EVERY SCENE MUST HAVE NON-EMPTY SUBTITLES. If you run out of story text, do not create additional scenes
        - Ensure that the scenes flow logically and capture the essence of the story or information
        - Make subtitles PLAIN TEXT only - do not use asterisks (*) for emphasis, or any other formatting symbols
        - Do not include vertical bars or column separators (|) in subtitles
        - Do not repeat the same subtitles across multiple scenes
        - Do not include directorial comments like "Scene:" or "Description:" in the subtitles
        {additional_guidelines.get(story_type, "")}

        Format your response as a JSON object with the following structure:
        {{
            "project_info": {{
                "title": "{title}",
                "user": "AI Generated",
                "timestamp": "{timestamp}"
            }},
            "storyboards": [
                {{
                    "scene_number": "Scene Number",
                    "description": "Detailed Visual Scene Description",
                    "subtitles": "Subtitles or Dialogue",
                    "image": null,
                    "audio": null,
                    "transition_type": "Transition Type"
                }},
                ...
            ]
        }}

        Here's the story:

    {story}"""

    messages = [
        {
            "role": "system",
            "content": '''You are a highly skilled storyboard artist specializing in visualizing various types of content. You excel at:
                1. Creating vivid, engaging scene descriptions that translate different types of content into compelling visuals
                2. Developing visual metaphors and analogies to represent complex ideas or concepts
                3. Crafting scenes that show the real-world application or implications of the information
                4. Incorporating basic cinematographic techniques to enhance visual storytelling
                5. Faithfully representing the original text using exact quotes for subtitles
                6. Ensuring the visual narrative accurately captures key points and their development
                7. Balancing informative content with visually interesting and engaging scenes
                8. Maintaining logical consistency between scenes while providing a variety of visual representations
                9. Using plain text only for subtitles - no formatting characters or symbols
                10. Never repeating the same subtitles in multiple scenes
                11. Avoiding any technical markers or formatting in subtitles (like asterisks, bars, or scene labels)
                12. Using only standard ASCII characters in all text (no special quotes, em dashes, etc.)
                13. Avoiding quotes within quotes in titles and descriptions to prevent JSON parsing issues

                Your storyboards effectively bridge the gap between various types of content and visual representation,
                paying close attention to both the overall flow of information and specific details that enhance
                the communication of the content.'''
        },
        {"role": "user", "content": prompt},
    ]

    response = call_openai_api(client, messages)

    try:
        # Try to extract JSON from the response
        match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
        if match:
            json_str = match.group(1)
        else:
            json_str = response

        # Log the raw JSON string for debugging
        logging.debug(f"Raw JSON string before processing: {json_str[:100]}...")

        # Look for the start of JSON content if there's text before it
        start_idx = json_str.find('{')
        if start_idx > 0:
            json_str = json_str[start_idx:]

        # Use our comprehensive JSON cleaner to fix all common issues
        json_str = clean_json_string(json_str)

        # The old code has been moved to json_cleaner.py

        # Clean up common JSON formatting issues
        # Replace single quotes with double quotes (common LLM mistake)
        json_str = re.sub(r"'([^']*)'\s*:", r'"\1":', json_str)

        # Fix trailing commas in arrays and objects (common LLM mistake)
        json_str = re.sub(r',\s*}', '}', json_str)
        json_str = re.sub(r',\s*\]', ']', json_str)

        # Replace special quotes with regular quotes
        json_str = json_str.replace('\u201c', '\"').replace('\u201d', '\"')
        json_str = json_str.replace('\u2018', "'").replace('\u2019', "'")

        # Fix missing quotes around property names
        json_str = re.sub(r'([{,])\s*(\w+)\s*:', r'\1"\2":', json_str)

        # Fix missing commas after description fields (common Groq issue)
        json_str = re.sub(r'"description":\s*"([^"]*)"\s*"subtitles"', r'"description": "\1","subtitles"', json_str)

        # Fix missing commas between any JSON properties (more general fix)
        json_str = re.sub(r'"([^"]+)":\s*"([^"]*)"\s*"([^"]+)":', r'"\1": "\2","\3":', json_str)

        # Fix missing commas after scene_number fields
        json_str = re.sub(r'"scene_number":\s*"([^"]*)"\s*"description"', r'"scene_number": "\1","description"', json_str)
        json_str = re.sub(r'"scene_number":\s*(\d+)\s*"description"', r'"scene_number": \1,"description"', json_str)

        # Fix missing commas after reading fields
        json_str = re.sub(r'"reading":\s*"([^"]*)"\s*"', r'"reading": "\1","', json_str)

        # Fix missing commas after message fields
        json_str = re.sub(r'"message":\s*"([^"]*)"\s*"', r'"message": "\1","', json_str)

        # Log the processed JSON string
        logging.debug(f"Processed JSON string: {json_str[:100]}...")

        # Try to parse the JSON
        try:
            storyboard_project = json.loads(json_str)
        except json.JSONDecodeError as json_err:
            logging.error(f"JSON parsing error: {json_err}")
            logging.error(f"Problematic JSON: {json_str}")

            # Create a basic storyboard structure as fallback
            storyboard_project = {
                "project_info": {
                    "title": title,
                    "user": "AI Generated",
                    "timestamp": timestamp
                },
                "storyboards": []
            }

            # Try to extract any scenes that might be in the response
            scene_matches = re.finditer(r'"scene_number"\s*:\s*(\d+)[^{]*"subtitles"\s*:\s*"([^"]+)"', json_str)
            for i, match in enumerate(scene_matches, 1):
                scene_num = int(match.group(1)) if match.group(1).isdigit() else i
                subtitles = match.group(2)

                storyboard_project["storyboards"].append({
                    "scene_number": scene_num,
                    "subtitles": subtitles,
                    "description": f"Scene {scene_num}",
                    "image_prompt": f"Scene {scene_num} visual"
                })

        # Clean up subtitles for all scenes
        for scene in storyboard_project.get("storyboards", []):
            if "subtitles" in scene and scene["subtitles"] is not None:
                # Remove any formatting symbols like asterisks, pipes, etc.
                scene["subtitles"] = scene["subtitles"].replace("*", "").replace("|", "").strip()

                # Remove any scene or directorial labels
                scene["subtitles"] = re.sub(r'^(Scene|Description|Subtitle|Voice over|VO):\s*', '', scene["subtitles"])

                # Handle any duplicate lines in the same subtitle (sometimes happens with philosophy content)
                lines = scene["subtitles"].split("\n")
                if len(lines) > 1:
                    # Check for duplicates and keep only unique lines
                    unique_lines = []
                    for line in lines:
                        if line not in unique_lines:
                            unique_lines.append(line)
                    scene["subtitles"] = " ".join(unique_lines)
            elif "subtitles" in scene and scene["subtitles"] is None:
                # If subtitles is None, set it to an empty string to avoid errors
                logging.warning(f"Found None subtitles in scene {scene.get('scene_number', 'unknown')}. Setting to empty string.")
                scene["subtitles"] = ""

        # Check for duplicate subtitles across scenes and fix if found
        subtitle_set = set()
        for scene in storyboard_project.get("storyboards", []):
            if "subtitles" in scene and scene["subtitles"] is not None:
                if scene["subtitles"] in subtitle_set:
                    # This is a duplicate, append "(continued)" to make it unique
                    scene["subtitles"] = scene["subtitles"] + " (continued)"
                subtitle_set.add(scene["subtitles"])

        return storyboard_project
    except Exception as e:
        logging.error(f"Error parsing storyboard JSON: {e}")
        # Log the full traceback for debugging
        logging.error("Traceback:")
        logging.error(traceback.format_exc())

        # Return an empty storyboard if parsing fails
        logging.info(f"Creating empty storyboard for title: {title}")
        return create_empty_storyboard(title, timestamp)

def generate_general_storyboard(client, title: str, story: str, character_names: List[str]) -> Dict[str, Any]:
    return generate_storyboard(client, title, story, "general", character_names)

def generate_philosophy_storyboard(client, title: str, story: str, character_names: List[str]) -> Dict[str, Any]:
    return generate_storyboard(client, title, story, "philosophy", character_names)

def generate_fun_facts_storyboard(client, title: str, story: str) -> Dict[str, Any]:
    return generate_storyboard(client, title, story, "fun facts")

def generate_life_pro_tips_storyboard(client, title: str, story: str) -> Dict[str, Any]:
    return generate_storyboard(client, title, story, "life pro tips")
