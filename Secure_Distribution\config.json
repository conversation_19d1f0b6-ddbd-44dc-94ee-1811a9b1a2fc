{"story_generation": {"char_limit_min": 600, "char_limit_max": 700}, "storyboard": {"max_scenes": 14}, "openai": {"model": "gpt-4o-mini", "temperature": 0.9}, "replicate_flux_api": {"model": "black-forest-labs/flux-schnell", "aspect_ratio": {"portrait": "9:16", "landscape": "16:9"}, "num_inference_steps": 4, "disable_safety_checker": false, "guidance": 3.0, "output_quality": 100}, "fal_flux_api": {"model": "fal-ai/flux/schnell", "image_size": {"portrait": "portrait_16_9", "landscape": "landscape_16_9"}, "num_inference_steps": 4, "guidance_scale": 3.5, "enable_safety_checker": false, "num_images": 1}, "together_flux_api": {"model": "black-forest-labs/FLUX.1-schnell-Free", "dimensions": {"portrait": {"width": 720, "height": 1280}, "landscape": {"width": 1280, "height": 720}}, "steps": 4}, "video_resolutions": {"720p": {"portrait": {"width": 720, "height": 1280}, "landscape": {"width": 1280, "height": 720}}, "1080p": {"portrait": {"width": 1080, "height": 1920}, "landscape": {"width": 1920, "height": 1080}}, "2K": {"portrait": {"width": 1440, "height": 2560}, "landscape": {"width": 2560, "height": 1440}}, "4K": {"portrait": {"width": 2160, "height": 3840}, "landscape": {"width": 3840, "height": 2160}}}, "tts": {"speech_rate": 1.0}, "elevenlabs": {"model": "eleven_turbo_v2_5", "available_models": [{"id": "eleven_multilingual_v2", "name": "Eleven Multilingual v2", "description": "Best for most use cases. Supports 29 languages with excellent stability and accent accuracy."}, {"id": "eleven_turbo_v2_5", "name": "Eleven Turbo v2.5", "description": "Fastest model with high quality. Ideal for real-time applications. Supports 32 languages."}, {"id": "eleven_monolingual_v1", "name": "Eleven Monolingual v1", "description": "English-only model optimized for quality and performance."}, {"id": "eleven_english_v2", "name": "Eleven English v2", "description": "Enhanced English-only model with improved quality."}], "voice_settings": {"stability": 0.5, "similarity_boost": 0.75, "style": 0.0, "use_speaker_boost": true}}}