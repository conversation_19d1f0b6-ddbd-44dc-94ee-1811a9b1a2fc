from PIL import Image, ImageDraw
import os

def create_play_icon():
    # Create a directory for assets if it doesn't exist
    script_dir = os.path.dirname(os.path.abspath(__file__))
    assets_dir = os.path.join(script_dir, "assets")
    os.makedirs(assets_dir, exist_ok=True)
    
    # Create a new image with a transparent background
    img = Image.new('RGBA', (64, 64), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Draw a play triangle
    play_color = (0, 120, 212)  # Blue color
    
    # Triangle points (play icon)
    points = [(16, 12), (16, 52), (48, 32)]
    
    # Draw the triangle
    draw.polygon(points, fill=play_color)
    
    # Save the image
    icon_path = os.path.join(assets_dir, "play_icon.png")
    img.save(icon_path)
    print(f"Play icon created at {icon_path}")

if __name__ == "__main__":
    create_play_icon()
