"""
ElevenLabs API client for text-to-speech functionality.
"""

import os
import logging
import requests
from typing import List, Dict, Any
from dotenv import load_dotenv
from utils import load_config

# Load environment variables from .env file
script_dir = os.path.dirname(os.path.abspath(__file__))
dotenv_path = os.path.join(os.path.dirname(script_dir), ".env")
load_dotenv(dotenv_path)

# Configure logging
logger = logging.getLogger(__name__)

# Load config
try:
    config = load_config()
    elevenlabs_config = config.get("elevenlabs", {})

    # Get model from config
    DEFAULT_MODEL = elevenlabs_config.get("model", "eleven_multilingual_v2")

    # Get voice settings from config
    voice_settings = elevenlabs_config.get("voice_settings", {})
    DEFAULT_STABILITY = voice_settings.get("stability", 0.5)
    DEFAULT_SIMILARITY = voice_settings.get("similarity_boost", 0.75)
    DEFAULT_STYLE = voice_settings.get("style", 0.0)
    DEFAULT_SPEAKER_BOOST = voice_settings.get("use_speaker_boost", True)

    logger.info(f"Loaded ElevenLabs config: model={DEFAULT_MODEL}")
except Exception as e:
    logger.warning(f"Error loading ElevenLabs config: {str(e)}. Using defaults.")
    # Default voice settings if config loading fails
    DEFAULT_STABILITY = 0.5
    DEFAULT_SIMILARITY = 0.75
    DEFAULT_STYLE = 0.0
    DEFAULT_SPEAKER_BOOST = True
    DEFAULT_MODEL = "eleven_multilingual_v2"

class ElevenLabsClient:
    """Client for interacting with the ElevenLabs API"""

    def __init__(self):
        """Initialize the ElevenLabs client"""
        self._initialize()

    def _initialize(self):
        """Internal initialization method"""
        # Reload environment variables to get the latest values
        load_dotenv(dotenv_path)

        self.api_key = os.getenv("ELEVENLABS_API_KEY", "")
        self.base_url = "https://api.elevenlabs.io/v1"

        # Add debug logging for API key detection
        if self.api_key:
            logger.info(f"ElevenLabs API key found: {self.api_key[:4]}...{self.api_key[-4:]}")
        else:
            logger.warning("ElevenLabs API key not found in environment variables")

        # Check if API key is valid using a direct API call
        self.is_available = self._check_api_key_direct()
        self.client = None

        if self.is_available:
            try:
                # Try to import and initialize the official client
                try:
                    from elevenlabs.client import ElevenLabs
                    self.client = ElevenLabs(api_key=self.api_key)
                    logger.info("ElevenLabs client initialized successfully")
                except ImportError:
                    logger.error("ElevenLabs Python package not installed. Please install with 'pip install elevenlabs'")
                    # We'll still set is_available to True if the direct API check worked
            except Exception as e:
                logger.error(f"Error initializing ElevenLabs client: {str(e)}")
                # We'll still keep is_available as True if the direct API check worked
        else:
            logger.warning("ElevenLabs API key not found or invalid. ElevenLabs TTS will not be available.")

    def reinitialize(self):
        """Reinitialize the client with updated environment variables"""
        logger.info("Reinitializing ElevenLabs client...")
        self._initialize()

    def _check_api_key_direct(self) -> bool:
        """Check if the API key is valid by making a direct API call"""
        if not self.api_key:
            return False

        try:
            logger.info("Checking ElevenLabs API key with direct API call...")
            headers = {
                "xi-api-key": self.api_key,
                "Content-Type": "application/json"
            }

            response = requests.get(
                f"{self.base_url}/voices",
                headers=headers
            )

            if response.status_code == 200:
                logger.info("ElevenLabs API key is valid (direct check)")
                return True
            else:
                logger.error(f"ElevenLabs API key check failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            logger.error(f"Error checking ElevenLabs API key: {str(e)}")
            return False

    def get_voices(self) -> List[Dict[str, Any]]:
        """Get all available voices from ElevenLabs"""
        if not self.is_available:
            logger.warning("ElevenLabs client not available (API key not set or invalid). Cannot get voices.")
            return []

        # Try using the client library first if available
        if self.client:
            try:
                logger.info("Attempting to get ElevenLabs voices using client library...")
                response = self.client.voices.get_all()
                logger.info(f"Successfully retrieved {len(response.voices)} voices from ElevenLabs")

                voices_list = [
                    {
                        "voice_id": voice.voice_id,
                        "name": voice.name,
                        "category": voice.category,
                        "description": voice.description or "",
                        "preview_url": voice.preview_url or "",
                        "labels": voice.labels or {}
                    }
                    for voice in response.voices
                ]

                # Log the first few voices for debugging
                if voices_list:
                    logger.info(f"First voice: {voices_list[0]['name']} ({voices_list[0]['voice_id']})")

                return voices_list
            except Exception as e:
                logger.error(f"Error getting ElevenLabs voices using client library: {str(e)}")
                logger.info("Falling back to direct API call...")
                # Fall back to direct API call

        # Use direct API call as fallback or primary method if client is not available
        try:
            logger.info("Getting ElevenLabs voices using direct API call...")
            headers = {
                "xi-api-key": self.api_key,
                "Content-Type": "application/json"
            }

            response = requests.get(
                f"{self.base_url}/voices",
                headers=headers
            )

            if response.status_code != 200:
                logger.error(f"Error getting ElevenLabs voices: {response.status_code} - {response.text}")
                return []

            data = response.json()
            voices = data.get("voices", [])
            logger.info(f"Successfully retrieved {len(voices)} voices from ElevenLabs API")

            voices_list = [
                {
                    "voice_id": voice.get("voice_id"),
                    "name": voice.get("name"),
                    "category": voice.get("category", "basic"),
                    "description": voice.get("description", ""),
                    "preview_url": voice.get("preview_url", ""),
                    "labels": voice.get("labels", {})
                }
                for voice in voices
            ]

            # Log the first few voices for debugging
            if voices_list:
                logger.info(f"First voice: {voices_list[0]['name']} ({voices_list[0]['voice_id']})")

            return voices_list
        except Exception as e:
            logger.error(f"Error getting ElevenLabs voices with direct API call: {str(e)}")
            # Print the full traceback for better debugging
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return []

    def generate_audio(
        self,
        text: str,
        voice_id: str,
        output_file: str,
        stability: float = DEFAULT_STABILITY,
        similarity: float = DEFAULT_SIMILARITY,
        style: float = DEFAULT_STYLE,
        speaker_boost: bool = DEFAULT_SPEAKER_BOOST,
        model: str = DEFAULT_MODEL
    ) -> bool:
        """
        Generate audio from text using ElevenLabs API

        Args:
            text: Text to convert to speech
            voice_id: ID of the voice to use
            output_file: Path to save the audio file
            stability: Voice stability (0.0 to 1.0)
            similarity: Voice similarity (0.0 to 1.0)
            style: Speaking style (0.0 to 1.0)
            speaker_boost: Whether to enhance speaker clarity
            model: Model ID to use for generation

        Returns:
            True if successful, False otherwise
        """
        if not self.is_available:
            logger.warning("ElevenLabs client not available (API key not set or invalid). Cannot generate audio.")
            return False

        # Try using the client library first if available
        if self.client:
            try:
                logger.info(f"Generating audio using client library with voice ID: {voice_id}")
                # Set voice settings
                voice_settings = {
                    "stability": stability,
                    "similarity_boost": similarity,
                    "style": style,
                    "use_speaker_boost": speaker_boost
                }

                # Generate audio
                audio = self.client.text_to_speech.convert(
                    text=text,
                    voice_id=voice_id,
                    model_id=model,
                    voice_settings=voice_settings,
                    output_format="mp3_44100_128"
                )

                # Save the audio to file
                with open(output_file, "wb") as f:
                    f.write(audio)

                logger.info(f"ElevenLabs audio saved to {output_file}")
                return True
            except Exception as e:
                logger.error(f"Error generating ElevenLabs audio using client library: {str(e)}")
                logger.info("Falling back to direct API call...")
                # Fall back to direct API call

        # Use direct API call as fallback or primary method if client is not available
        try:
            logger.info(f"Generating audio using direct API call with voice ID: {voice_id}")

            # Set voice settings
            voice_settings = {
                "stability": stability,
                "similarity_boost": similarity,
                "style": style,
                "use_speaker_boost": speaker_boost
            }

            # Prepare request
            headers = {
                "xi-api-key": self.api_key,
                "Content-Type": "application/json",
                "Accept": "audio/mpeg"
            }

            data = {
                "text": text,
                "model_id": model,
                "voice_settings": voice_settings
            }

            # Make the API request
            response = requests.post(
                f"{self.base_url}/text-to-speech/{voice_id}",
                json=data,
                headers=headers
            )

            if response.status_code != 200:
                logger.error(f"Error generating ElevenLabs audio: {response.status_code} - {response.text}")
                return False

            # Save the audio to file
            with open(output_file, "wb") as f:
                f.write(response.content)

            logger.info(f"ElevenLabs audio saved to {output_file} using direct API call")
            return True

        except Exception as e:
            logger.error(f"Error generating ElevenLabs audio with direct API call: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False

    def generate_preview(self, voice_id: str, output_file: str, preview_text: str = "Hello, this is a preview of my voice.") -> bool:
        """
        Generate a preview audio file for a voice

        Args:
            voice_id: ID of the voice to preview
            output_file: Path to save the preview audio
            preview_text: Text to use for the preview

        Returns:
            True if successful, False otherwise
        """
        return self.generate_audio(
            text=preview_text,
            voice_id=voice_id,
            output_file=output_file
        )

# Initialize the client
elevenlabs_client = ElevenLabsClient()
