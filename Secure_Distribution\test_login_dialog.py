#!/usr/bin/env python3
"""
Test script for the enhanced login dialog with login and purchase buttons.
This script demonstrates the new functionality added to the login dialog.
"""

import sys
import os
import tkinter as tk
from ttkthemes import ThemedTk

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from auth import show_login_dialog
    print("✓ Successfully imported login dialog")
except ImportError as e:
    print(f"❌ Error importing login dialog: {e}")
    sys.exit(1)

def test_login_dialog():
    """Test the enhanced login dialog"""
    print("\n🧪 Testing Enhanced Login Dialog")
    print("=" * 50)
    
    # Create root window
    root = ThemedTk(theme="arc")
    root.title("Login Dialog Test")
    root.geometry("400x300")
    
    # Center the window
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - 400) // 2
    y = (screen_height - 300) // 2
    root.geometry(f"400x300+{x}+{y}")
    
    # Create test interface
    main_frame = tk.Frame(root, bg='#f0f0f0')
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    title_label = tk.Label(
        main_frame,
        text="Enhanced Login Dialog Test",
        font=('Arial', 16, 'bold'),
        bg='#f0f0f0'
    )
    title_label.pack(pady=(0, 20))
    
    info_text = """
This test demonstrates the new login dialog features:

✅ Modern UI design with floating labels
✅ Email and password input fields
✅ Login button with form validation
✅ Purchase button for unregistered emails
✅ Real-time email registration checking
✅ Smooth transitions and animations

Click the button below to test the login dialog.
    """
    
    info_label = tk.Label(
        main_frame,
        text=info_text.strip(),
        font=('Arial', 10),
        bg='#f0f0f0',
        justify=tk.LEFT,
        wraplength=350
    )
    info_label.pack(pady=(0, 20))
    
    def test_dialog():
        """Test the login dialog"""
        print("\n🔐 Opening login dialog...")
        result = show_login_dialog(root)
        
        if result:
            print("✅ Login successful!")
            success_label = tk.Label(
                main_frame,
                text="✅ Login Successful!",
                font=('Arial', 12, 'bold'),
                fg='green',
                bg='#f0f0f0'
            )
            success_label.pack(pady=10)
        else:
            print("❌ Login failed or cancelled")
            fail_label = tk.Label(
                main_frame,
                text="❌ Login Failed or Cancelled",
                font=('Arial', 12, 'bold'),
                fg='red',
                bg='#f0f0f0'
            )
            fail_label.pack(pady=10)
    
    # Test button
    test_btn = tk.Button(
        main_frame,
        text="🧪 Test Login Dialog",
        command=test_dialog,
        font=('Arial', 12, 'bold'),
        bg='#4CAF50',
        fg='white',
        padx=20,
        pady=10,
        cursor='hand2'
    )
    test_btn.pack(pady=10)
    
    # Exit button
    exit_btn = tk.Button(
        main_frame,
        text="❌ Exit Test",
        command=root.quit,
        font=('Arial', 10),
        bg='#f44336',
        fg='white',
        padx=15,
        pady=5,
        cursor='hand2'
    )
    exit_btn.pack(pady=(10, 0))
    
    print("✓ Test interface created")
    print("📋 Features to test:")
    print("   • Email field with floating label")
    print("   • Password field with floating label")
    print("   • Login button (enabled when form is valid)")
    print("   • Purchase button (shown for unregistered emails)")
    print("   • Real-time email validation")
    print("   • Modern UI styling and animations")
    
    # Start the test
    root.mainloop()

if __name__ == "__main__":
    test_login_dialog()
