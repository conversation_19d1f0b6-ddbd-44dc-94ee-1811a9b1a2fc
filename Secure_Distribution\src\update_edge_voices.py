import asyncio
import edge_tts
import json
import os

async def main():
    """Fetch all English voices from Edge TTS and save them to a file"""
    print("Fetching all English voices from Edge TTS...")
    
    # Get all available voices
    voices = await edge_tts.list_voices()
    
    # Filter to only English voices
    english_voices = [v for v in voices if v["Locale"].startswith("en-")]
    
    print(f"Found {len(english_voices)} English voices")
    
    # Organize voices by locale
    organized_voices = {}
    for voice in english_voices:
        locale = voice["Locale"]
        gender = voice["Gender"]
        name = voice["ShortName"]
        friendly_name = voice["FriendlyName"]
        
        if locale not in organized_voices:
            organized_voices[locale] = {"Male": [], "Female": []}
        
        organized_voices[locale][gender].append({
            "short_name": name,
            "friendly_name": friendly_name
        })
    
    # Print organized voices
    for locale, genders in organized_voices.items():
        print(f"\n{locale}:")
        for gender, voices in genders.items():
            if voices:
                print(f"  {gender}:")
                for voice in voices:
                    print(f"    {voice['short_name']} - {voice['friendly_name']}")
    
    # Format for direct use in code
    voices_for_code = []
    for voice in english_voices:
        name = voice["ShortName"]
        friendly_name = voice["FriendlyName"].replace("(", "-").replace(")", "")
        voices_for_code.append(f'"{name}", # {friendly_name}')
    
    print("\nVoices for code usage:")
    print("[\n    " + ",\n    ".join(voices_for_code) + "\n]")
    
    # Write to JSON file for easy use
    script_dir = os.path.dirname(os.path.abspath(__file__))
    json_path = os.path.join(script_dir, "english_edge_voices.json")
    
    with open(json_path, "w") as f:
        json.dump(english_voices, f, indent=2)
    
    print(f"\nVoice details saved to {json_path}")
    
    # Create a Python file with the voice list for easy import
    py_path = os.path.join(script_dir, "edge_voices.py")
    
    with open(py_path, "w") as f:
        f.write("# List of all English voices available in Edge TTS\n\n")
        f.write("EDGE_VOICES = [\n    ")
        f.write(",\n    ".join(voices_for_code))
        f.write("\n]\n\n")
        
        # Add a function to get voices by locale
        f.write("def get_voices_by_locale(locale=None):\n")
        f.write("    \"\"\"Get voices filtered by locale (e.g., 'en-US', 'en-GB')\n")
        f.write("    If locale is None, returns all voices\n")
        f.write("    \"\"\"\n")
        f.write("    if locale is None:\n")
        f.write("        return EDGE_VOICES\n")
        f.write("    return [voice for voice in EDGE_VOICES if voice.startswith(locale)]\n")
    
    print(f"Voice list for code usage saved to {py_path}")

if __name__ == "__main__":
    asyncio.run(main())
