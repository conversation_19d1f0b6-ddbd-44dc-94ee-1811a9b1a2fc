import sys 
import os 
import shutil 
import base64 
import zlib 
import random 
import string 
 
def obfuscate_file(input_file, output_file): 
    """Simple obfuscation using base64 and zlib compression""" 
    try: 
        with open(input_file, 'r', encoding='utf-8') as f: 
            content = f.read() 
ECHO is off.
        # Simple obfuscation: compress and encode 
        compressed = zlib.compress(content.encode('utf-8')) 
        encoded = base64.b64encode(compressed).decode('ascii') 
ECHO is off.
        # Create obfuscated wrapper 
        wrapper = f"""import base64, zlib; exec^(zlib.decompress^(base64.b64decode^('{encoded}'^)^).decode^('utf-8'^)^)""" 
ECHO is off.
        # Ensure output directory exists 
        os.makedirs(os.path.dirname(output_file), exist_ok=True) 
ECHO is off.
        with open(output_file, 'w', encoding='utf-8') as f: 
            f.write(wrapper) 
ECHO is off.
        return True 
    except Exception as e: 
        print(f"Obfuscation failed: {e}") 
        # Fallback: copy original file 
        try: 
            os.makedirs(os.path.dirname(output_file), exist_ok=True) 
            shutil.copy2(input_file, output_file) 
            return True 
        except Exception as copy_error: 
            print(f"Copy fallback failed: {copy_error}") 
            return False 
 
if __name__ == "__main__": 
    if len(sys.argv) == 2 and sys.argv[1] == "--help": 
        print("Usage: python obfuscator.py input_file output_file") 
        sys.exit(0) 
    elif len(sys.argv)  
        print("Usage: python obfuscator.py input_file output_file") 
        sys.exit(1) 
ECHO is off.
    input_file = sys.argv[1] 
    output_file = sys.argv[2] 
ECHO is off.
    if obfuscate_file(input_file, output_file): 
        print(f"Successfully processed {input_file} to {output_file}") 
    else: 
        print(f"Failed to process {input_file}") 
        sys.exit(1) 
