import os
import json
import base64

class CredentialsManager:
    """
    Manages user credentials with simple storage and retrieval.
    Uses basic encoding (not encryption) to store email addresses locally.
    """
    def __init__(self):
        self.app_data_dir = self._get_app_data_dir()
        self.credentials_file = os.path.join(self.app_data_dir, "credentials.dat")
        self._ensure_app_dir_exists()

    def _get_app_data_dir(self):
        """Get the application data directory based on the OS"""
        if os.name == 'nt':  # Windows
            app_data = os.path.join(os.environ.get('APPDATA', ''), 'AzanxAutoShorts')
        else:  # macOS and Linux
            app_data = os.path.join(os.path.expanduser('~'), '.azanxautoshorts')
        return app_data

    def _ensure_app_dir_exists(self):
        """Ensure the application data directory exists"""
        if not os.path.exists(self.app_data_dir):
            os.makedirs(self.app_data_dir)

    def _simple_encode(self, text):
        """Simple encoding of text (not secure, just to obscure)"""
        return base64.b64encode(text.encode()).decode()

    def _simple_decode(self, encoded_text):
        """Simple decoding of text"""
        try:
            return base64.b64decode(encoded_text.encode()).decode()
        except:
            return None

    def save_credentials(self, email):
        """
        Save the user's email to the credentials file

        Args:
            email: The user's email address
        """
        try:
            # Encode the email (simple obfuscation, not secure encryption)
            encoded_email = self._simple_encode(email)

            # Create credentials data
            credentials_data = {
                "email": encoded_email,
                "logged_in": True
            }

            # Save to file
            with open(self.credentials_file, 'w') as f:
                json.dump(credentials_data, f)

            return True
        except Exception as e:
            print(f"Error saving credentials: {e}")
            return False

    def get_credentials(self):
        """
        Get the saved credentials if they exist

        Returns:
            dict: The credentials data or None if not found
        """
        if not os.path.exists(self.credentials_file):
            return None

        try:
            with open(self.credentials_file, 'r') as f:
                credentials_data = json.load(f)

            # Decode the email
            encoded_email = credentials_data.get("email", "")
            email = self._simple_decode(encoded_email)

            if not email:
                return None

            return {
                "email": email,
                "logged_in": credentials_data.get("logged_in", False)
            }
        except Exception as e:
            print(f"Error loading credentials: {e}")
            return None

    def clear_credentials(self):
        """Clear the saved credentials"""
        if os.path.exists(self.credentials_file):
            try:
                os.remove(self.credentials_file)
                return True
            except Exception as e:
                print(f"Error clearing credentials: {e}")
                return False
        return True

    def is_logged_in(self):
        """Check if the user is logged in"""
        credentials = self.get_credentials()
        return credentials is not None and credentials.get("logged_in", False)

    def get_email(self):
        """Get the saved email address"""
        credentials = self.get_credentials()
        if credentials:
            return credentials.get("email")
        return None
