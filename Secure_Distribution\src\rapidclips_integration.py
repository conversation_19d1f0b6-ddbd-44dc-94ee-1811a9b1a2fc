"""
Integration module for RapidClips image generation approach with Repic Clip integration.
This module provides functions to generate images using the RapidClips approach with
enhanced prompt generation that analyzes story content to create optimal image prompts.
"""
import os
import json
from typing import List, Dict, Any
from services.prompt_service import PromptService
from services.image_service import get_image_service

def generate_images_for_story(
    story: Dict[str, Any],
    image_service_name: str = "replicate",
    orientation: str = "portrait",
    video_quality: str = "720p",
    image_style: str = "cinematic",
    check_pause_func=None,
    check_stop_func=None
) -> List[str]:
    """
    Generate images for a story using the RapidClips approach with Repic Clip integration.
    The AI analyzes the story content to create prompts that prioritize all types of images
    while using the selected image style.

    Args:
        story: The story data
        image_service_name: The name of the image service to use ('replicate', 'together', or 'fal')
        orientation: Orientation of the video (portrait, landscape)
        video_quality: Quality of the video (720p, 1080p, 2K, 4K)
        image_style: The desired visual style for the images (e.g., "cinematic", "anime", "islamic")
        check_pause_func: Optional function to check if generation should pause
        check_stop_func: Optional function to check if generation should stop

    Returns:
        List of image file paths
    """
    # Initialize services
    prompt_service = PromptService()
    image_service = get_image_service(image_service_name)

    # Get story directory
    story_dir = story.get("story_dir")
    if not story_dir:
        raise ValueError("Story directory not specified in story data")

    # Create directory if it doesn't exist
    os.makedirs(story_dir, exist_ok=True)

    # Get storyboards
    storyboards = story.get("storyboards", [])
    if not storyboards:
        raise ValueError("No storyboards found in story data")

    # Get dimensions based on orientation and video quality
    dimensions = get_dimensions(orientation, video_quality)
    width, height = dimensions["width"], dimensions["height"]

    print(f"Using {image_service_name} image service with dimensions: {width}x{height}")

    # Initialize list to store image file paths
    image_files = []

    # Initialize list to store previous prompts
    previous_prompts = []

    # Create full subtitles text for context
    full_subtitles = "\n".join([board.get("subtitles", "") for board in storyboards])

    # Get story type if available (for Islamic content support)
    story_type = story.get("story_type", "general")
    print(f"Story type: {story_type}")

    # If story type contains Islamic keywords, use Islamic style
    if any(islamic_keyword in story_type.lower() for islamic_keyword in ["islamic", "islam", "hadith", "quran", "muslim"]):
        print(f"Islamic content detected, using Islamic style regardless of selected style")
        image_style = "Islamic"

    print(f"Using image style: {image_style}")

    # Generate images for each storyboard
    for i, storyboard in enumerate(storyboards):
        # Check if we should stop
        if check_stop_func and check_stop_func():
            print("Image generation stopped by user")
            return image_files

        # Check if we should pause
        if check_pause_func:
            check_pause_func()

        # Get subtitles for this storyboard
        subtitles = storyboard.get("subtitles", "")

        # Get additional context from the storyboard if available
        scene_description = storyboard.get("description", "")

        # Combine subtitles with scene description for richer context
        scene_context = subtitles
        if scene_description:
            scene_context = f"{scene_description}\n{subtitles}"

        # Generate image prompt with the specified style using Repic Clip integration
        # This will analyze the story content to create optimal image prompts
        image_prompt = prompt_service.generate_image_prompt(
            full_subtitles=full_subtitles,
            previous_prompts=previous_prompts,
            group_text=scene_context,
            image_style=image_style
        )

        # Add to previous prompts
        previous_prompts.append(image_prompt)

        # Log the prompt
        print(f"Image prompt for scene {i+1}: {image_prompt}")

        # Create a safe scene number for the image file path
        scene_num = storyboard.get('scene_number', i+1)
        safe_scene_num = str(scene_num).replace(" ", "_")

        # Define image filename
        image_filename = os.path.join(story_dir, f"scene_{safe_scene_num}.png")

        # Generate image
        try:
            print(f"Generating image for scene {i+1} using {image_service_name} service")

            # Check if we should stop before making the API call
            if check_stop_func and check_stop_func():
                print("Image generation stopped by user")
                return image_files

            # Generate image with the selected service
            image_data = image_service.generate_image(
                prompt=image_prompt,
                width=width,
                height=height,
                steps=4  # Default steps
            )

            # Check if we should stop after the API call
            if check_stop_func and check_stop_func():
                print("Image generation stopped by user")
                return image_files

            # Save image
            if image_data:
                # Save image
                with open(image_filename, "wb") as f:
                    f.write(image_data)

                print(f"Image saved to {image_filename}")
                image_files.append(image_filename)

                # Update storyboard with image path
                storyboard['image'] = image_filename
            else:
                print(f"Failed to generate image for scene {i+1} with {image_service_name}")

                # Try fallback to Replicate if the primary service fails
                if image_service_name != "replicate":
                    print(f"Trying fallback to Replicate for scene {i+1}")
                    try:
                        # Get Replicate service
                        replicate_service = get_image_service("replicate")

                        # Check if we should stop before trying the fallback
                        if check_stop_func and check_stop_func():
                            print("Image generation stopped by user")
                            return image_files

                        # Check if we should pause before the fallback
                        if check_pause_func:
                            check_pause_func()

                        # Generate image with Replicate
                        fallback_image_data = replicate_service.generate_image(
                            prompt=image_prompt,
                            width=width,
                            height=height,
                            steps=4
                        )

                        # Check if we should stop after the fallback API call
                        if check_stop_func and check_stop_func():
                            print("Image generation stopped by user")
                            return image_files

                        if fallback_image_data:
                            # Save image
                            with open(image_filename, "wb") as f:
                                f.write(fallback_image_data)

                            print(f"Fallback image saved to {image_filename}")
                            image_files.append(image_filename)

                            # Update storyboard with image path
                            storyboard['image'] = image_filename
                        else:
                            print(f"Fallback also failed for scene {i+1}")
                    except Exception as fallback_error:
                        print(f"Error with fallback for scene {i+1}: {fallback_error}")
        except Exception as e:
            print(f"Error generating image for scene {i+1}: {e}")

            # Try fallback to Replicate if the primary service fails with an exception
            if image_service_name != "replicate":
                print(f"Trying fallback to Replicate for scene {i+1} after exception")
                try:
                    # Get Replicate service
                    replicate_service = get_image_service("replicate")

                    # Check if we should stop before trying the fallback
                    if check_stop_func and check_stop_func():
                        print("Image generation stopped by user")
                        return image_files

                    # Check if we should pause before the fallback
                    if check_pause_func:
                        check_pause_func()

                    # Generate image with Replicate
                    fallback_image_data = replicate_service.generate_image(
                        prompt=image_prompt,
                        width=width,
                        height=height,
                        steps=4
                    )

                    # Check if we should stop after the fallback API call
                    if check_stop_func and check_stop_func():
                        print("Image generation stopped by user")
                        return image_files

                    if fallback_image_data:
                        # Save image
                        with open(image_filename, "wb") as f:
                            f.write(fallback_image_data)

                        print(f"Fallback image saved to {image_filename}")
                        image_files.append(image_filename)

                        # Update storyboard with image path
                        storyboard['image'] = image_filename
                    else:
                        print(f"Fallback also failed for scene {i+1}")
                except Exception as fallback_error:
                    print(f"Error with fallback for scene {i+1}: {fallback_error}")

    # Save updated story data
    story_file = os.path.join(story_dir, "storyboard_project.json")
    with open(story_file, "w", encoding="utf-8") as f:
        json.dump(story, f, indent=2, ensure_ascii=False)

    return image_files

def get_dimensions(orientation: str, video_quality: str) -> Dict[str, int]:
    """
    Get image dimensions based on orientation and video quality.

    Args:
        orientation: Orientation of the video (portrait, landscape)
        video_quality: Quality of the video (720p, 1080p, 2K, 4K)

    Returns:
        Dictionary with width and height
    """
    # Define base dimensions for portrait orientation
    dimensions = {
        "720p": {"portrait": (720, 1280), "landscape": (1280, 720)},
        "1080p": {"portrait": (1080, 1920), "landscape": (1920, 1080)},
        "2K": {"portrait": (1440, 2560), "landscape": (2560, 1440)},
        "4K": {"portrait": (2160, 3840), "landscape": (3840, 2160)}
    }

    # Get dimensions based on orientation and quality
    quality_dims = dimensions.get(video_quality, dimensions["720p"])
    width, height = quality_dims.get(orientation, quality_dims["portrait"])

    # Calculate aspect ratio before adjustment
    original_aspect_ratio = width / height

    # Ensure width and height are multiples of 16 for Together AI
    width = (width // 16) * 16
    height = (height // 16) * 16

    # Check if aspect ratio has changed significantly
    new_aspect_ratio = width / height
    if abs(new_aspect_ratio - original_aspect_ratio) > 0.01:
        # Recalculate height to maintain aspect ratio
        height = int(width / original_aspect_ratio)
        # Make height a multiple of 16
        height = (height // 16) * 16
        # If height is now 0, set it to 16
        if height == 0:
            height = 16
            width = int(height * original_aspect_ratio)
            width = max(16, (width // 16) * 16)

    print(f"Adjusted dimensions: {width}x{height}, aspect ratio: {width/height:.2f}")

    return {"width": width, "height": height}
