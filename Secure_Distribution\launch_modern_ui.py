#!/usr/bin/env python3
"""
Launch script for the Modern 1ClickVideo UI
Contemporary interface with Material Design principles
"""

import sys
import os
import traceback

def check_dependencies():
    """Check if required modern UI dependencies are installed"""
    missing_deps = []

    try:
        import ttkthemes
    except ImportError:
        missing_deps.append("ttkthemes>=3.2.2")

    try:
        import PIL
    except ImportError:
        missing_deps.append("Pillow>=10.2.0")

    if missing_deps:
        print("Missing required dependencies for Modern UI:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\nPlease install them using:")
        print(f"pip install {' '.join(missing_deps)}")
        return False

    return True

def main():
    """Main entry point for Modern UI launcher"""
    print("=" * 55)
    print("  Modern 1ClickVideo v6.5 - Contemporary UI")
    print("=" * 55)
    print()

    # Check dependencies first
    if not check_dependencies():
        input("Press Enter to exit...")
        sys.exit(1)

    # Add src directory to path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    src_path = os.path.join(current_dir, 'src')
    if src_path not in sys.path:
        sys.path.insert(0, src_path)

    try:
        print("Loading Modern UI components...")
        # Import and run the modern UI
        from modern_ui import main as modern_main
        print("Starting Modern UI application...")
        print()
        modern_main()

    except ImportError as e:
        print(f"Error importing modern UI: {e}")
        print("Make sure all files are in the correct location.")
        traceback.print_exc()
        input("Press Enter to exit...")
        sys.exit(1)

    except Exception as e:
        print(f"Error starting Modern UI: {e}")
        traceback.print_exc()
        input("Press Enter to exit...")
        sys.exit(1)

if __name__ == "__main__":
    main()
