import tkinter as tk
from tkinter import ttk, messagebox, font, colorchooser
import threading
import os
import sys
import platform
from dotenv import load_dotenv
from ttkthemes import ThemedTk
import webbrowser

# Import from main.py
from main import (
    generate_story, generate_storyboard, process_video,
    replicate_flux_api, fal_flux_api, together_flux_api,
    APP_NAME, APP_VERSION, DEFAULT_FONT
)

# Import voice selection widget
from voice_selection_widget import VoiceSelectionWidget
import voice_preview

class VideoGeneratorThread(threading.Thread):
    def __init__(self, story_type, image_style, voice_name, font_name, callback):
        super().__init__()
        self.story_type = story_type
        self.image_style = image_style
        self.voice_name = voice_name
        self.font_name = font_name
        self.callback = callback

    def run(self):
        try:
            # Initialize OpenAI client
            load_dotenv()
            client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

            # Generate story
            self.callback("Generating story...")
            story = generate_story(client, self.story_type)
            story["image_style"] = self.image_style

            # Generate storyboard
            self.callback("Creating storyboard...")
            storyboard_project = generate_storyboard(client, story)

            # Create video
            self.callback("Creating video...")
            story_dir = storyboard_project["story_dir"]
            audio_dir = os.path.join(story_dir, "audio")
            video_path = os.path.join(story_dir, "story_video.mp4")
            process_video(client, storyboard_project, video_path, audio_dir, self.voice_name, self.font_name)

            self.callback("Video generation completed!")
            messagebox.showinfo("Success", "Video generation completed successfully!")

        except Exception as e:
            messagebox.showerror("Error", f"An error occurred: {str(e)}")

class CardFrame(ttk.Frame):
    """Material Design card with optional title, elevation, and rounded corners"""
    def __init__(self, master, title=None, padding=16, **kwargs):
        # Create a frame with appropriate style
        super().__init__(master, style='Card.TFrame', padding=padding, **kwargs)
        
        # Add inner padding and configure column
        self.columnconfigure(0, weight=1)
        
        # Add title if provided with Material Design styling
        if title:
            # Create a title frame with proper styling
            title_frame = ttk.Frame(self, style='Card.TFrame')
            title_frame.grid(row=0, column=0, sticky='ew', pady=(0, 10))
            
            # Title with Material Design typography
            title_label = ttk.Label(title_frame, text=title, style='CardTitle.TLabel')
            title_label.pack(side=tk.LEFT, anchor='w')
            
            # Add a subtle divider
            separator = ttk.Separator(self, orient=tk.HORIZONTAL)
            separator.grid(row=1, column=0, sticky='ew', pady=(0, 12))
            
            # Content will start at row 2
            self.content_row = 2
        else:
            # Content will start at row 0
            self.content_row = 0

class ModernButton(tk.Button):
    """Material Design button with hover effects"""
    def __init__(self, master, text="Button", command=None, button_type="primary", width=None, **kwargs):
        self.button_type = button_type
        
        # Set colors based on button type
        if button_type == "primary":
            bg_color = "#2196F3"  # Blue
            fg_color = "white"
            active_bg = "#1976D2"  # Darker blue
            active_fg = "white"
        elif button_type == "secondary":
            bg_color = "#757575"  # Gray
            fg_color = "white"
            active_bg = "#616161"  # Darker gray
            active_fg = "white"
        elif button_type == "danger":
            bg_color = "#F44336"  # Red
            fg_color = "white"
            active_bg = "#D32F2F"  # Darker red
            active_fg = "white"
        else:  # Default
            bg_color = "#2196F3"  # Blue
            fg_color = "white"
            active_bg = "#1976D2"  # Darker blue
            active_fg = "white"
        
        # Initialize with all parameters
        super().__init__(
            master,
            text=text,
            command=command,
            font=(DEFAULT_FONT, 12),
            bg=bg_color,
            fg=fg_color,
            activebackground=active_bg,
            activeforeground=active_fg,
            relief=tk.FLAT,
            borderwidth=0,
            padx=15,
            pady=8,
            width=width,
            cursor="hand2",
            **kwargs
        )
        
        # Add hover effect
        self.bind("<Enter>", self._on_enter)
        self.bind("<Leave>", self._on_leave)
    
    def _on_enter(self, e):
        if self.button_type == "primary":
            self.config(background="#1976D2")  # Darker blue
        elif self.button_type == "secondary":
            self.config(background="#616161")  # Darker gray
        elif self.button_type == "danger":
            self.config(background="#D32F2F")  # Darker red
    
    def _on_leave(self, e):
        if self.button_type == "primary":
            self.config(background="#2196F3")  # Blue
        elif self.button_type == "secondary":
            self.config(background="#757575")  # Gray
        elif self.button_type == "danger":
            self.config(background="#F44336")  # Red

class MainWindow:
    def __init__(self, root):
        self.root = root
        self.root.title("1ClickVideo by Galib")
        self.root.geometry("1000x700")
        
        # Set window size and position - center on screen
        window_width = 1000
        window_height = 700
        
        # Get screen dimensions
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # Calculate position to center the window
        center_x = int(screen_width/2 - window_width/2)
        center_y = int(screen_height/2 - window_height/2)
        
        # Set geometry (width x height + x_offset + y_offset)
        self.root.geometry(f"{window_width}x{window_height}+{center_x}+{center_y}")
        
        # Set minimum size
        self.root.minsize(900, 600)
        
        # Configure dark theme styles
        self.setup_dark_theme()

    def setup_dark_theme(self):
        """Configure dark theme styles for the application"""
        style = ttk.Style()
        
        # Define colors
        bg_color = "#1E1E1E"  # Dark background
        card_color = "#2A2A2A"  # Card background
        text_color = "#FFFFFF"  # White text
        text_secondary = "#B0B0B0"  # Light gray text
        accent_color = "#2196F3"  # Blue accent
        border_color = "#3A3A3A"  # Dark border
        
        # Configure base styles
        style.configure('TFrame', background=bg_color)
        style.configure('Card.TFrame', background=card_color)
        style.configure('TLabel', background=bg_color, foreground=text_color)
        style.configure('Card.TLabel', background=card_color, foreground=text_color)
        style.configure('TButton', font=(DEFAULT_FONT, 12))
        style.configure('TCheckbutton', background=bg_color, foreground=text_color)
        style.configure('TRadiobutton', background=bg_color, foreground=text_color)
        
        # Configure typography styles
        style.configure('Title.TLabel', font=(DEFAULT_FONT, 24, 'bold'), background=bg_color, foreground=text_color)
        style.configure('Subtitle.TLabel', font=(DEFAULT_FONT, 18), background=bg_color, foreground=text_color)
        style.configure('CardTitle.TLabel', font=(DEFAULT_FONT, 14, 'bold'), background=card_color, foreground=text_color)
        style.configure('FormField.TLabel', font=(DEFAULT_FONT, 12), background=card_color, foreground=text_color)
        
        # Configure combobox
        style.configure('TCombobox', fieldbackground=card_color, foreground=text_color)
        style.map('TCombobox',
                 fieldbackground=[('readonly', card_color)],
                 selectbackground=[('readonly', accent_color)],
                 selectforeground=[('readonly', text_color)])
        
        # Configure entry
        style.configure('TEntry', fieldbackground=card_color, foreground=text_color)
        
        # Configure separator
        style.configure('TSeparator', background=border_color)
        
        # Configure progressbar
        style.configure('TProgressbar', background=accent_color, troughcolor=border_color)
        
        # Set root background color
        self.root.configure(background=bg_color)
        
        # Create main container with dark background
        self.main_container = ttk.Frame(self.root, padding="20")
        self.main_container.pack(fill=tk.BOTH, expand=True)

        # App title
        title_frame = ttk.Frame(self.main_container)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        title = ttk.Label(title_frame, text="1ClickVideo by Galib", style='Title.TLabel')
        title.pack(side=tk.LEFT)
        
        # Create two-column layout
        columns_frame = ttk.Frame(self.main_container)
        columns_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        columns_frame.columnconfigure(0, weight=1)
        columns_frame.columnconfigure(1, weight=1)
        
        # Left column
        left_column = ttk.Frame(columns_frame)
        left_column.grid(row=0, column=0, sticky="nsew", padx=10)
        
        # Right column
        right_column = ttk.Frame(columns_frame)
        right_column.grid(row=0, column=1, sticky="nsew", padx=10)
        
        # 1. API Selection Card
        api_card = CardFrame(left_column, title="API Selection")
        api_card.pack(fill=tk.X, pady=10)
        
        # Script API
        api_grid = ttk.Frame(api_card, style='Card.TFrame')
        api_grid.grid(row=api_card.content_row, column=0, sticky="ew")
        
        ttk.Label(api_grid, text="Script API:", style='FormField.TLabel').grid(row=0, column=0, sticky="w", pady=10)
        self.script_api_combo = ttk.Combobox(api_grid, values=["gpt-4"], state="readonly", width=15)
        self.script_api_combo.grid(row=0, column=1, sticky="w", pady=10, padx=5)
        self.script_api_combo.current(0)
        
        # Image API
        ttk.Label(api_grid, text="Image API:", style='FormField.TLabel').grid(row=1, column=0, sticky="w", pady=10)
        self.image_api_combo = ttk.Combobox(api_grid, values=["together_ai"], state="readonly", width=15)
        self.image_api_combo.grid(row=1, column=1, sticky="w", pady=10, padx=5)
        self.image_api_combo.current(0)
        
        # Voice API
        ttk.Label(api_grid, text="Voice API:", style='FormField.TLabel').grid(row=2, column=0, sticky="w", pady=10)
        self.voice_api_combo = ttk.Combobox(api_grid, values=["OpenAI", "Voicely", "ElevenLabs"], state="readonly", width=15)
        self.voice_api_combo.grid(row=2, column=1, sticky="w", pady=10, padx=5)
        self.voice_api_combo.current(0)
        
        # 2. Video Settings Card
        video_card = CardFrame(left_column, title="Video Settings")
        video_card.pack(fill=tk.X, pady=10)
        
        video_grid = ttk.Frame(video_card, style='Card.TFrame')
        video_grid.grid(row=video_card.content_row, column=0, sticky="ew")
        
        # Aspect Ratio
        ttk.Label(video_grid, text="Aspect Ratio:", style='FormField.TLabel').grid(row=0, column=0, sticky="w", pady=10)
        self.aspect_ratio_combo = ttk.Combobox(video_grid, values=["Landscape (16:9)", "Portrait", "Square"], state="readonly", width=15)
        self.aspect_ratio_combo.grid(row=0, column=1, sticky="w", pady=10, padx=5)
        self.aspect_ratio_combo.current(0)
        
        # Video Quality
        ttk.Label(video_grid, text="Video Quality:", style='FormField.TLabel').grid(row=1, column=0, sticky="w", pady=10)
        self.quality_combo = ttk.Combobox(video_grid, values=["720p", "1080p", "2K", "4K"], state="readonly", width=15)
        self.quality_combo.grid(row=1, column=1, sticky="w", pady=10, padx=5)
        self.quality_combo.current(0)
        
        # Background Music
        ttk.Label(video_grid, text="Background Music:", style='FormField.TLabel').grid(row=2, column=0, sticky="w", pady=10)
        self.bg_music_combo = ttk.Combobox(video_grid, values=["None"], state="readonly", width=15)
        self.bg_music_combo.grid(row=2, column=1, sticky="w", pady=10, padx=5)
        self.bg_music_combo.current(0)
        
        # Music Volume
        ttk.Label(video_grid, text="Music Volume:", style='FormField.TLabel').grid(row=3, column=0, sticky="w", pady=10)
        self.volume_scale = ttk.Scale(video_grid, from_=0, to=100, orient=tk.HORIZONTAL)
        self.volume_scale.grid(row=3, column=1, sticky="w", pady=10, padx=5)
        self.volume_scale.set(20)  # Default 20%
        
        # Duration
        ttk.Label(video_grid, text="Duration:", style='FormField.TLabel').grid(row=4, column=0, sticky="w", pady=10)
        self.duration_combo = ttk.Combobox(video_grid, values=["11 minutes"], state="readonly", width=15)
        self.duration_combo.grid(row=4, column=1, sticky="w", pady=10, padx=5)
        self.duration_combo.current(0)
        
        # Style
        ttk.Label(video_grid, text="Style:", style='FormField.TLabel').grid(row=5, column=0, sticky="w", pady=10)
        self.style_combo = ttk.Combobox(video_grid, values=[
            "realistic", "Photorealistic", "Cinematic", "Anime",
            "Comic Book", "Pixar Art"
        ], state="readonly", width=15)
        self.style_combo.grid(row=5, column=1, sticky="w", pady=10, padx=5)
        self.style_combo.current(0)
        
        # Story Type
        ttk.Label(video_grid, text="Story Type:", style='FormField.TLabel').grid(row=6, column=0, sticky="w", pady=10)
        self.story_combo = ttk.Combobox(video_grid, values=[
            "Scary", "Mystery", "Bedtime", "Interesting History",
            "Urban Legends", "Motivational", "Fun Facts",
            "Long Form Jokes", "Life Pro Tips", "Philosophy", "Love",
            "Islamic"
        ], state="readonly", width=15)
        self.story_combo.grid(row=6, column=1, sticky="w", pady=10, padx=5)
        self.story_combo.current(5)  # Default to Motivational
        
        # 3. Voice Settings Card
        voice_card = CardFrame(right_column, title="Voice Settings")
        voice_card.pack(fill=tk.X, pady=10)
        
        voice_grid = ttk.Frame(voice_card, style='Card.TFrame')
        voice_grid.grid(row=voice_card.content_row, column=0, sticky="ew")
        
        # Voice Selection
        ttk.Label(voice_grid, text="Voice:", style='FormField.TLabel').grid(row=0, column=0, sticky="w", pady=10)
        
        # Voice selection with preview button
        voice_selection_frame = ttk.Frame(voice_grid, style='Card.TFrame')
        voice_selection_frame.grid(row=0, column=1, sticky="w", pady=10, padx=5)
        
        self.voice_combo = VoiceSelectionWidget(voice_selection_frame, values=[
            "Alloy", "Echo", "Fable", "Onyx", "Nova", "Shimmer"
        ], width=15)
        self.voice_combo.pack(fill=tk.X)
        self.voice_combo.set("Alloy")  # Default voice
        
        # Speech Rate
        ttk.Label(voice_grid, text="Speech Rate:", style='FormField.TLabel').grid(row=1, column=0, sticky="w", pady=10)
        self.speech_rate_scale = ttk.Scale(voice_grid, from_=0, to=100, orient=tk.HORIZONTAL)
        self.speech_rate_scale.grid(row=1, column=1, sticky="w", pady=10, padx=5)
        self.speech_rate_scale.set(100)  # Default 100%
        
        # Filter by Location
        ttk.Label(voice_grid, text="Filter by Location:", style='FormField.TLabel').grid(row=2, column=0, sticky="w", pady=10)
        self.location_combo = ttk.Combobox(voice_grid, values=["All"], state="readonly", width=15)
        self.location_combo.grid(row=2, column=1, sticky="w", pady=10, padx=5)
        self.location_combo.current(0)
        
        # 4. Subtitle Settings Card
        subtitle_card = CardFrame(right_column, title="Subtitle Settings")
        subtitle_card.pack(fill=tk.X, pady=10)
        
        subtitle_grid = ttk.Frame(subtitle_card, style='Card.TFrame')
        subtitle_grid.grid(row=subtitle_card.content_row, column=0, sticky="ew")
        
        # Font Selection
        ttk.Label(subtitle_grid, text="Subtitle Font:", style='FormField.TLabel').grid(row=0, column=0, sticky="w", pady=10)
        available_fonts = [
            "TitanOne", "Ranchers", "RampartOne",
            "PermanentMarker", "OpenSans", "NotoSans",
            "Montserrat", "LuckiestGuy", "Knewave",
            "Jua", "Creepster", "Caveat",
            "Bungee", "BebasNeue", "Bangers",
            "BakbakOne"
        ]
        self.font_combo = ttk.Combobox(subtitle_grid, values=available_fonts, state="readonly", width=15)
        self.font_combo.grid(row=0, column=1, sticky="w", pady=10, padx=5)
        self.font_combo.set("TitanOne")
        
        # Font Color
        ttk.Label(subtitle_grid, text="Font Color:", style='FormField.TLabel').grid(row=1, column=0, sticky="w", pady=10)
        self.font_color_button = tk.Button(subtitle_grid, text="Choose Color", bg="#FFFFFF", fg="#000000", command=self.choose_font_color)
        self.font_color_button.grid(row=1, column=1, sticky="w", pady=10, padx=5)
        self.font_color = "#FFFFFF"  # Default white
        
        # Font Size
        ttk.Label(subtitle_grid, text="Font Size:", style='FormField.TLabel').grid(row=2, column=0, sticky="w", pady=10)
        self.font_size_combo = ttk.Combobox(subtitle_grid, values=["50", "40", "30", "20"], state="readonly", width=15)
        self.font_size_combo.grid(row=2, column=1, sticky="w", pady=10, padx=5)
        self.font_size_combo.current(0)  # Default 50
        
        # Outline Color
        ttk.Label(subtitle_grid, text="Outline Color:", style='FormField.TLabel').grid(row=3, column=0, sticky="w", pady=10)
        self.outline_color_button = tk.Button(subtitle_grid, text="Choose Color", bg="#000000", fg="#FFFFFF", command=self.choose_outline_color)
        self.outline_color_button.grid(row=3, column=1, sticky="w", pady=10, padx=5)
        self.outline_color = "#000000"  # Default black
        
        # Outline Size
        ttk.Label(subtitle_grid, text="Outline Size:", style='FormField.TLabel').grid(row=4, column=0, sticky="w", pady=10)
        self.outline_size_combo = ttk.Combobox(subtitle_grid, values=["3", "2", "1", "0"], state="readonly", width=15)
        self.outline_size_combo.grid(row=4, column=1, sticky="w", pady=10, padx=5)
        self.outline_size_combo.current(0)  # Default 3
        
        # Bottom controls frame
        controls_frame = ttk.Frame(self.main_container)
        controls_frame.pack(fill=tk.X, pady=20, side=tk.BOTTOM)
        
        # Progress Bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(controls_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))
        
        # Status Label
        self.status_label = ttk.Label(controls_frame, text="Ready to generate video")
        self.status_label.pack(pady=(0, 10))
        
        # Action buttons
        buttons_frame = ttk.Frame(controls_frame)
        buttons_frame.pack(fill=tk.X)
        
        # Generate Button
        self.generate_button = ModernButton(buttons_frame, text="GENERATE VIDEO", command=self.generate_video, button_type="primary", width=20)
        self.generate_button.pack(side=tk.LEFT, padx=5)
        
        # Pause Button
        self.pause_button = ModernButton(buttons_frame, text="PAUSE", command=self.pause_generation, button_type="secondary", width=10)
        self.pause_button.pack(side=tk.LEFT, padx=5)
        self.pause_button.config(state=tk.DISABLED)
        
        # Stop Button
        self.stop_button = ModernButton(buttons_frame, text="STOP", command=self.stop_generation, button_type="danger", width=10)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        self.stop_button.config(state=tk.DISABLED)

        # Initialize video generator thread
        self.generator_thread = None

    def choose_font_color(self):
        """Open color chooser dialog for font color"""
        color = colorchooser.askcolor(initialcolor=self.font_color, title="Choose Font Color")[1]
        if color:
            self.font_color = color
            self.font_color_button.config(bg=color)
            # Update text color for better visibility
            text_color = "#000000" if self._is_light_color(color) else "#FFFFFF"
            self.font_color_button.config(fg=text_color)
    
    def choose_outline_color(self):
        """Open color chooser dialog for outline color"""
        color = colorchooser.askcolor(initialcolor=self.outline_color, title="Choose Outline Color")[1]
        if color:
            self.outline_color = color
            self.outline_color_button.config(bg=color)
            # Update text color for better visibility
            text_color = "#000000" if self._is_light_color(color) else "#FFFFFF"
            self.outline_color_button.config(fg=text_color)
    
    def _is_light_color(self, hex_color):
        """Check if a color is light or dark to determine text color"""
        # Convert hex to RGB
        hex_color = hex_color.lstrip('#')
        r, g, b = int(hex_color[0:2], 16), int(hex_color[2:4], 16), int(hex_color[4:6], 16)
        # Calculate brightness (perceived luminance)
        brightness = (0.299 * r + 0.587 * g + 0.114 * b) / 255
        return brightness > 0.5  # Return True if color is light
    
    def update_status(self, message):
        """Update the status message"""
        self.status_label.config(text=message)
        self.root.update()
    
    def pause_generation(self):
        """Pause the video generation process"""
        if self.generator_thread and self.generator_thread.is_alive():
            # Implement pause functionality here
            messagebox.showinfo("Pause", "Generation paused. Click Resume to continue.")
            # Change button text to Resume
            self.pause_button.config(text="RESUME", command=self.resume_generation)
    
    def resume_generation(self):
        """Resume the video generation process"""
        if self.generator_thread and self.generator_thread.is_alive():
            # Implement resume functionality here
            messagebox.showinfo("Resume", "Generation resumed.")
            # Change button text back to Pause
            self.pause_button.config(text="PAUSE", command=self.pause_generation)
    
    def stop_generation(self):
        """Stop the video generation process"""
        if self.generator_thread and self.generator_thread.is_alive():
            # Implement stop functionality here
            if messagebox.askyesno("Confirm", "Are you sure you want to stop the generation process?"):
                messagebox.showinfo("Stop", "Generation stopped.")
                self.update_status("Generation stopped by user")
                # Reset UI state
                self.generate_button.config(state=tk.NORMAL)
                self.pause_button.config(state=tk.DISABLED)
                self.stop_button.config(state=tk.DISABLED)
    
    def generate_video(self):
        """Generate a video based on selected options"""
        # Validate selections
        if not self.story_combo.get():
            messagebox.showerror("Error", "Please select a story type")
            return
        if not self.style_combo.get():
            messagebox.showerror("Error", "Please select an image style")
            return
        if not self.voice_combo.get():
            messagebox.showerror("Error", "Please select a voice")
            return
        
        # Disable the generate button and enable pause/stop buttons
        self.generate_button.config(state=tk.DISABLED)
        self.pause_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_var.set(0)
        
        # Get selected options
        story_type = self.story_combo.get()
        image_style = self.style_combo.get()
        voice_name = self.voice_combo.get()
        font_name = self.font_combo.get()
        font_size = int(self.font_size_combo.get())
        outline_size = int(self.outline_size_combo.get())
        
        # Update status
        self.update_status(f"Generating {story_type} video with {image_style} style...")
        
        # Create and start the generator thread
        self.generator_thread = VideoGeneratorThread(
            story_type, image_style, voice_name, font_name, self.update_status
        )
        self.generator_thread.start()
        
        # Start a timer to check the thread status
        self.root.after(1000, self.check_thread_status)

    def check_thread_status(self):
        """Check if the generator thread has completed"""
        if self.generator_thread and self.generator_thread.is_alive():
            # Thread is still running, update progress
            # For demonstration, we'll just increment the progress
            current_progress = self.progress_var.get()
            if current_progress < 100:
                self.progress_var.set(current_progress + 1)
            
            # Check again after 100ms
            self.root.after(100, self.check_thread_status)
        else:
            # Thread has completed or hasn't started
            if self.generator_thread:
                # Thread completed
                self.progress_var.set(100)
                self.update_status("Video generation completed!")
                
                # Reset UI state
                self.generate_button.config(state=tk.NORMAL)
                self.pause_button.config(state=tk.DISABLED)
                self.stop_button.config(state=tk.DISABLED)

def main():
    # Use a dark theme that matches the image
    root = ThemedTk(theme="equilux")  # equilux is a dark theme
    
    # Set window title
    root.title("1ClickVideo by Galib")
    
    # Create the application
    app = MainWindow(root)
    
    # Start the main loop
    root.mainloop()

if __name__ == "__main__":
    main()