#!/usr/bin/env python3
"""
Test script for Modern UI build verification
Tests all modern UI components and functionality
"""

import sys
import os
import traceback

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import ttkthemes
        print("✓ ttkthemes imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import ttkthemes: {e}")
        return False
    
    try:
        import PIL
        print("✓ Pillow imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import Pillow: {e}")
        return False
    
    try:
        import tkinter as tk
        print("✓ tkinter imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import tkinter: {e}")
        return False
    
    return True

def test_modern_ui_components():
    """Test modern UI components"""
    print("\nTesting Modern UI components...")
    
    # Add src to path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    src_path = os.path.join(current_dir, 'src')
    if src_path not in sys.path:
        sys.path.insert(0, src_path)
    
    try:
        from modern_ui import ModernColors, ModernButton, ModernCard
        print("✓ Modern UI components imported successfully")
        
        # Test ModernColors
        assert hasattr(ModernColors, 'PRIMARY'), "ModernColors.PRIMARY not found"
        assert hasattr(ModernColors, 'BG_PRIMARY'), "ModernColors.BG_PRIMARY not found"
        assert hasattr(ModernColors, 'TEXT_PRIMARY'), "ModernColors.TEXT_PRIMARY not found"
        print("✓ ModernColors class verified")
        
        # Test that color values are valid hex colors
        assert ModernColors.PRIMARY.startswith('#'), "PRIMARY color is not a valid hex color"
        assert len(ModernColors.PRIMARY) == 7, "PRIMARY color is not a valid 6-digit hex color"
        print("✓ ModernColors values verified")
        
        return True
        
    except ImportError as e:
        print(f"✗ Failed to import Modern UI components: {e}")
        return False
    except AssertionError as e:
        print(f"✗ Modern UI component verification failed: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error testing Modern UI components: {e}")
        return False

def test_modern_ui_creation():
    """Test creating modern UI components"""
    print("\nTesting Modern UI component creation...")
    
    try:
        import tkinter as tk
        from ttkthemes import ThemedTk
        
        # Add src to path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        src_path = os.path.join(current_dir, 'src')
        if src_path not in sys.path:
            sys.path.insert(0, src_path)
        
        from modern_ui import ModernColors, ModernButton, ModernCard
        
        # Create test window (don't show it)
        root = ThemedTk(theme="arc")
        root.withdraw()  # Hide the window
        
        # Test ModernCard creation
        card = ModernCard(root, title="Test Card")
        print("✓ ModernCard created successfully")
        
        # Test ModernButton creation
        button = ModernButton(
            card,
            text="Test Button",
            command=lambda: None,
            style="primary"
        )
        print("✓ ModernButton created successfully")
        
        # Clean up
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ Failed to create Modern UI components: {e}")
        traceback.print_exc()
        return False

def test_launch_script():
    """Test if launch script exists and is valid"""
    print("\nTesting launch script...")
    
    if not os.path.exists("launch_modern_ui.py"):
        print("✗ launch_modern_ui.py not found")
        return False
    
    print("✓ launch_modern_ui.py found")
    
    try:
        with open("launch_modern_ui.py", 'r') as f:
            content = f.read()
            if "modern_ui" in content and "main" in content:
                print("✓ launch_modern_ui.py content verified")
                return True
            else:
                print("✗ launch_modern_ui.py content invalid")
                return False
    except Exception as e:
        print(f"✗ Error reading launch_modern_ui.py: {e}")
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print("  Modern UI Build Verification Test")
    print("=" * 60)
    print()
    
    tests = [
        ("Import Test", test_imports),
        ("Modern UI Components Test", test_modern_ui_components),
        ("Modern UI Creation Test", test_modern_ui_creation),
        ("Launch Script Test", test_launch_script),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"Running {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} PASSED")
            else:
                print(f"✗ {test_name} FAILED")
        except Exception as e:
            print(f"✗ {test_name} FAILED with exception: {e}")
        print()
    
    print("=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Modern UI build is ready.")
        return True
    else:
        print("❌ Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    input("Press Enter to exit...")
    sys.exit(0 if success else 1)
