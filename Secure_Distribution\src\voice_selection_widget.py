import tkinter as tk
from tkinter import ttk
import threading
import voice_preview

class VoiceSelectionWidget(ttk.Frame):
    """
    Custom widget for voice selection with preview functionality
    """
    def __init__(self, master, values=None, command=None, width=25, **kwargs):
        super().__init__(master, **kwargs)

        self.values = values or []
        self.command = command
        self.width = width
        self.selected_value = tk.StringVar()
        self.current_playing = None

        # Create the combobox
        self.combo = ttk.Combobox(
            self,
            textvariable=self.selected_value,
            values=self.values,
            state="readonly",
            width=width
        )
        self.combo.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Create the play button with text
        self.play_button = ttk.Button(
            self,
            text="▶",
            width=3,
            command=self.play_selected_voice
        )
        self.play_button.pack(side=tk.LEFT, padx=(5, 0))

        # Bind events
        self.combo.bind("<<ComboboxSelected>>", self._on_selection_changed)

    def _on_selection_changed(self, event=None):
        """Handle selection change in the combobox"""
        if self.command:
            self.command(self.selected_value.get())

    def play_selected_voice(self):
        """Play a preview of the currently selected voice"""
        voice = self.selected_value.get()
        if not voice:
            return

        # Get the TTS model from the parent window
        tts_model = "Voicely"  # Default
        try:
            # Try to get the TTS model from the parent window
            parent = self.winfo_toplevel()
            if hasattr(parent, 'tts_model_combo'):
                tts_model = parent.tts_model_combo.get()
            elif hasattr(parent, 'tts_model_var'):
                tts_model = parent.tts_model_var.get()
        except Exception:
            pass

        # Check for ElevenLabs error states - don't play preview for these
        if tts_model == "ElevenLabs":
            error_states = [
                "Loading ElevenLabs voices...",
                "ElevenLabs API key not set",
                "ElevenLabs service unavailable",
                "No ElevenLabs voices found",
                "ElevenLabs client not installed",
                "Error loading ElevenLabs voices"
            ]

            if voice in error_states:
                # Don't play preview for error states
                return

        # Get the actual voice ID if we're using a display name
        voice_id = voice
        try:
            parent = self.winfo_toplevel()

            # Handle ElevenLabs voice mapping
            if tts_model == "ElevenLabs" and hasattr(parent, 'elevenlabs_voice_mapping'):
                if voice in parent.elevenlabs_voice_mapping:
                    voice_id = parent.elevenlabs_voice_mapping[voice]
                else:
                    print(f"Warning: ElevenLabs voice '{voice}' not found in mapping")
                    return
            elif hasattr(parent, 'voice_display_to_id') and voice in parent.voice_display_to_id:
                voice_id = parent.voice_display_to_id[voice]
            elif tts_model == "Voicely" and not voice.startswith("en-"):
                # For Edge TTS (Voicely), ensure we have the full voice ID with locale prefix
                # If we don't have a mapping, try to find a default locale prefix
                voice_id = f"en-US-{voice}"  # Default to US English if no mapping exists
                print(f"Using default locale prefix for voice: {voice_id}")
        except Exception as e:
            print(f"Error getting voice ID: {str(e)}")

        # Update button state
        self.play_button.config(state=tk.DISABLED)

        # Play the preview
        def on_playback_complete():
            # Re-enable the play button when playback completes
            self.play_button.config(state=tk.NORMAL)

        # Start preview generation and playback in a separate thread
        threading.Thread(
            target=lambda: voice_preview.generate_and_play_preview(
                voice_id, tts_model, on_playback_complete
            ),
            daemon=True
        ).start()

    def get(self):
        """Get the currently selected value"""
        return self.selected_value.get()

    def set(self, value):
        """Set the selected value"""
        if value in self.values:
            self.selected_value.set(value)

    def configure(self, **kwargs):
        """Configure the widget"""
        if 'values' in kwargs:
            self.values = kwargs.pop('values')
            self.combo.configure(values=self.values)

        if 'state' in kwargs:
            state = kwargs.pop('state')
            self.combo.configure(state=state)
            # Also update play button state
            if state == 'disabled':
                self.play_button.configure(state=tk.DISABLED)
            else:
                self.play_button.configure(state=tk.NORMAL)

        super().configure(**kwargs)

    def current(self, index):
        """Set the selected item by index"""
        if 0 <= index < len(self.values):
            self.combo.current(index)

    # Forward other combobox methods
    def __getattr__(self, name):
        if hasattr(self.combo, name):
            return getattr(self.combo, name)
        raise AttributeError(f"{self.__class__.__name__} has no attribute '{name}'")
