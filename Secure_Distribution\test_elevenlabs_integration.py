#!/usr/bin/env python3
"""
Test script for ElevenLabs integration in the modern UI
"""

import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_elevenlabs_client():
    """Test the ElevenLabs client functionality"""
    print("Testing ElevenLabs client...")

    try:
        from elevenlabs_client import elevenlabs_client

        print(f"API Key available: {bool(elevenlabs_client.api_key)}")
        print(f"Client available: {elevenlabs_client.is_available}")

        if elevenlabs_client.is_available:
            print("Fetching voices...")
            voices = elevenlabs_client.get_voices()

            if voices:
                print(f"Found {len(voices)} voices:")
                for i, voice in enumerate(voices[:5]):  # Show first 5 voices
                    print(f"  {i+1}. {voice.get('name')} (ID: {voice.get('voice_id')})")
                if len(voices) > 5:
                    print(f"  ... and {len(voices) - 5} more voices")
            else:
                print("No voices found")
        else:
            print("ElevenLabs client not available")

    except ImportError as e:
        print(f"Import error: {e}")
    except Exception as e:
        print(f"Error: {e}")

def test_modern_ui_integration():
    """Test the modern UI ElevenLabs integration"""
    print("\nTesting Modern UI integration...")

    try:
        import tkinter as tk
        from ttkthemes import ThemedTk
        from modern_ui import ModernApp

        # Create a minimal test
        root = ThemedTk(theme="arc")
        root.withdraw()  # Hide the window

        app = ModernApp(root)

        # Test voice loading
        print("Testing voice loading methods...")

        # Set TTS model to ElevenLabs and load voices synchronously for testing
        app.tts_model_var.set("ElevenLabs")
        app.load_elevenlabs_voices_sync()  # Use synchronous method for testing

        print(f"Current voices: {len(app.current_voices)}")
        if app.current_voices:
            print(f"First voice: {app.current_voices[0]}")

        # Test voice mapping
        if hasattr(app, 'elevenlabs_voice_mapping'):
            print(f"Voice mapping available: {len(app.elevenlabs_voice_mapping)} voices")
            if app.elevenlabs_voice_mapping:
                first_voice_name = list(app.elevenlabs_voice_mapping.keys())[0]
                first_voice_id = app.elevenlabs_voice_mapping[first_voice_name]
                print(f"Example mapping: '{first_voice_name}' -> '{first_voice_id}'")
        else:
            print("No voice mapping available")

        # Test voice metadata
        if hasattr(app, 'elevenlabs_voice_metadata'):
            print(f"Voice metadata available: {len(app.elevenlabs_voice_metadata)} voices")
            if app.elevenlabs_voice_metadata:
                first_voice_name = list(app.elevenlabs_voice_metadata.keys())[0]
                metadata = app.elevenlabs_voice_metadata[first_voice_name]
                print(f"Example metadata: '{first_voice_name}' -> Category: {metadata.get('category')}")
        else:
            print("No voice metadata available")

        # Test model recommendations
        recommendation = app.get_elevenlabs_model_recommendation()
        print(f"Model recommendation: {recommendation['model']} ({recommendation['reason']})")

        # Test text validation
        test_text = "This is a test script for ElevenLabs integration."
        is_valid, message = app.validate_elevenlabs_text_length(test_text)
        print(f"Text validation: {is_valid} - {message}")

        root.destroy()
        print("Modern UI integration test completed")

    except Exception as e:
        print(f"Modern UI test error: {e}")

def test_voice_selection_widget():
    """Test the voice selection widget"""
    print("\nTesting Voice Selection Widget...")

    try:
        import tkinter as tk
        from voice_selection_widget import VoiceSelectionWidget

        root = tk.Tk()
        root.withdraw()  # Hide the window

        # Create test widget
        widget = VoiceSelectionWidget(
            root,
            values=["Test Voice 1", "Test Voice 2", "ElevenLabs API key not set"],
            width=25
        )

        # Test error state handling
        widget.set("ElevenLabs API key not set")
        current_value = widget.get()
        print(f"Widget handles error states: {current_value == 'ElevenLabs API key not set'}")

        root.destroy()
        print("Voice Selection Widget test completed")

    except Exception as e:
        print(f"Voice Selection Widget test error: {e}")

def main():
    """Run all tests"""
    print("=" * 50)
    print("ElevenLabs Integration Test Suite")
    print("=" * 50)

    test_elevenlabs_client()
    test_modern_ui_integration()
    test_voice_selection_widget()

    print("\n" + "=" * 50)
    print("Test suite completed")
    print("=" * 50)

if __name__ == "__main__":
    main()
