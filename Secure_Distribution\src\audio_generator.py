import os
import asyncio
import re
from dotenv import load_dotenv
from utils import load_config
import replicate
import requests
import edge_tts
import ai_client
from elevenlabs_client import elevenlabs_client

load_dotenv()

def handle_contractions(text):
    """
    Process text to properly handle contractions for TTS.

    This function was previously used to modify contractions to help Edge TTS pronounce them correctly.
    However, testing has shown that Edge TTS handles contractions correctly without any modifications.

    Args:
        text: The text to process

    Returns:
        The original text, unchanged
    """
    # Simply return the original text as Edge TTS handles contractions correctly
    return text

def clean_text_for_tts(text):
    """
    Clean text before sending to TTS to avoid emojis and special characters being read aloud.

    This function removes:
    - Emoji characters
    - Hashtags and their content (e.g., #coffee, #mindblown, #faceless-videos.app)
    - Special characters that might be read aloud incorrectly
    - <PERSON>perly handles contractions for better pronunciation

    Args:
        text: The text to clean

    Returns:
        Cleaned text suitable for TTS
    """
    # Remove emojis and other special Unicode characters
    # This regex pattern matches emoji characters and other symbols
    emoji_pattern = re.compile(
        "["
        "\U0001F600-\U0001F64F"  # emoticons
        "\U0001F300-\U0001F5FF"  # symbols & pictographs
        "\U0001F680-\U0001F6FF"  # transport & map symbols
        "\U0001F700-\U0001F77F"  # alchemical symbols
        "\U0001F780-\U0001F7FF"  # Geometric Shapes
        "\U0001F800-\U0001F8FF"  # Supplemental Arrows-C
        "\U0001F900-\U0001F9FF"  # Supplemental Symbols and Pictographs
        "\U0001FA00-\U0001FA6F"  # Chess Symbols
        "\U0001FA70-\U0001FAFF"  # Symbols and Pictographs Extended-A
        "\*********-\U000027B0"  # Dingbats
        "\U000024C2-\U0001F251"
        "]+", flags=re.UNICODE
    )
    text = emoji_pattern.sub(r'', text)

    # Replace asterisks with spaces (to avoid reading "star" or "asterisk")
    text = text.replace('*', ' ')

    # Remove hashtags and their content (e.g., #coffee, #mindblown, #faceless-videos.app)
    # This pattern matches hashtags and the words that follow them, including those with hyphens, underscores, and dots
    # Handle hashtags that appear after spaces
    hashtag_pattern = re.compile(r'\s#[\w\-\.]+')
    text = hashtag_pattern.sub(' ', text)

    # Handle hashtags at the beginning of the text
    hashtag_pattern_start = re.compile(r'^#[\w\-\.]+')
    text = hashtag_pattern_start.sub('', text)

    # Remove any remaining # symbols
    text = text.replace('#', '')

    # Only remove specific problematic symbols while preserving important punctuation
    # This preserves apostrophes, colons, semicolons, and other important punctuation
    # Only remove characters that cause issues with TTS systems
    text = re.sub(r'[<>|@$%^&\\{}[\]~`]', ' ', text)  # Remove only specific problematic characters

    # Clean up extra whitespace
    text = re.sub(r'\s+', ' ', text).strip()

    # Handle contractions for better pronunciation
    text = handle_contractions(text)

    return text

async def generate_edge_tts_audio(text, output_file, voice_name, speed=1.0, max_retries=5):
    """Generate audio using Voicely TTS (Microsoft voices)"""
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    # Clean the text before sending to TTS
    cleaned_text = clean_text_for_tts(text)

    # Convert speed to percentage string (1.0 = "+0%", 1.1 = "+10%", etc.)
    speed_str = f"{(speed - 1.0) * 100:+.0f}%"
    # Voicely TTS requires a sign even for 0%
    if speed == 1.0:
        speed_str = "+0%"

    # Check if the voice name needs the locale prefix
    if not voice_name.startswith("en-"):
        # Try to load the voice mapping from the JSON file
        try:
            import json
            script_dir = os.path.dirname(os.path.abspath(__file__))
            json_path = os.path.join(script_dir, "english_edge_voices.json")

            if os.path.exists(json_path):
                with open(json_path, "r") as f:
                    english_voices = json.load(f)

                # Look for a voice that ends with the provided name
                full_voice_name = None
                for voice in english_voices:
                    if voice["ShortName"].endswith(voice_name):
                        full_voice_name = voice["ShortName"]
                        break

                if full_voice_name:
                    print(f"Mapped voice name '{voice_name}' to full voice ID: {full_voice_name}")
                    voice_name = full_voice_name
        except Exception as mapping_error:
            print(f"Error mapping voice name: {str(mapping_error)}")
            # Continue with the original voice name

    # Retry logic for the selected voice
    original_error = None
    for attempt in range(max_retries):
        try:
            if attempt > 0:
                print(f"Retry attempt {attempt+1}/{max_retries} for voice: {voice_name}")
            else:
                print(f"Generating audio with Voicely TTS, voice: {voice_name}, speed: {speed_str}")

            # Configure communication with Voicely TTS
            communicate = edge_tts.Communicate(text=cleaned_text, voice=voice_name, rate=speed_str)

            # Save audio to file
            await communicate.save(output_file)

            print(f"Voicely TTS audio saved to {output_file}")
            return True
        except Exception as e:
            original_error = e
            print(f"Error generating Voicely TTS audio (attempt {attempt+1}/{max_retries}): {str(e)}")
            # Add a small delay before retrying
            await asyncio.sleep(1)

    # If we've exhausted all retries with the selected voice, try with a default voice
    # Only fall back to default voice after all retries with the selected voice have failed
    try:
        # Use a reliable default voice
        default_voice = "en-US-AriaNeural"
        print(f"All {max_retries} attempts with voice '{voice_name}' failed. Falling back to default voice: {default_voice}")

        # Configure communication with default voice
        communicate = edge_tts.Communicate(text=cleaned_text, voice=default_voice, rate=speed_str)

        # Save audio to file
        await communicate.save(output_file)

        print(f"Voicely TTS audio saved with fallback voice to {output_file}")
        return True
    except Exception as fallback_error:
        # If even the fallback fails, raise an exception with both errors
        raise Exception(
            f"Error generating audio with Voicely TTS after {max_retries} attempts with voice '{voice_name}': {str(original_error)}. "
            f"Fallback to default voice '{default_voice}' also failed: {str(fallback_error)}"
        )

def generate_audio(client, text, output_file, voice_name, tts_model="OpenAI"):
    """
    Generate audio from text using TTS service

    Args:
        client: The client (OpenAI or Groq) - not used if ai_client is used instead
        text: Text to convert to speech
        output_file: Path to save the audio
        voice_name: Voice to use for speech
        tts_model: TTS model/service to use ("OpenAI", "Groq", "Voicely", or "ElevenLabs")

    Returns:
        True if successful
    """
    config = load_config()
    # Get the speech rate from the config file
    speech_rate = config.get('tts', {}).get('speech_rate', 1.0)

    # Clean the text before sending to any TTS service
    cleaned_text = clean_text_for_tts(text)
    print(f"Original text: '{text[:50]}...'")
    print(f"Cleaned text for TTS: '{cleaned_text[:50]}...'")

    # Check if we're using an OpenAI voice (regardless of TTS model selection)
    # List of OpenAI voices
    openai_voices = ["alloy", "echo", "fable", "onyx", "nova", "shimmer"]

    # Check if the voice is an OpenAI voice (case insensitive)
    is_openai_voice = voice_name.lower() in [v.lower() for v in openai_voices]

    # Save the current provider to restore it later if needed
    current_provider = ai_client.get_current_provider()
    provider_switched = False

    try:
        # If using an OpenAI voice, always use OpenAI TTS regardless of the selected TTS model
        if is_openai_voice or tts_model == "OpenAI":
            print(f"Using OpenAI TTS with voice: {voice_name}")

            # If current provider is not OpenAI, temporarily switch to OpenAI
            if current_provider != "openai" and ai_client.openai_client:
                print(f"Temporarily switching to OpenAI for TTS (from {current_provider})")
                ai_client.set_provider("openai")
                provider_switched = True
                client = ai_client.get_current_client()  # Get the OpenAI client

            # Use OpenAI TTS
            if ai_client.openai_client:
                result = client.audio.speech.create(
                    model="tts-1",
                    voice=voice_name,
                    input=cleaned_text,
                    speed=speech_rate,
                    response_format="mp3"
                )

                # Save the audio content to the output file
                with open(output_file, "wb") as audio_file:
                    audio_file.write(result.content)

                print(f"OpenAI speech synthesized and saved to [{output_file}]")

                # Restore the original provider if we switched
                if provider_switched:
                    print(f"Restoring original provider: {current_provider}")
                    ai_client.set_provider(current_provider)

                return True
            else:
                raise Exception("OpenAI client not available. Please set OPENAI_API_KEY in .env file.")

        # If using Voicely TTS model with a non-OpenAI voice
        elif tts_model == "Voicely":
            print(f"Using Voicely TTS with voice: {voice_name}")
            # Call Voicely TTS asynchronously - the function already cleans the text
            asyncio.run(generate_edge_tts_audio(text, output_file, voice_name, speech_rate, max_retries=5))
            return True

        # If using ElevenLabs TTS model
        elif tts_model == "ElevenLabs":
            print(f"Using ElevenLabs TTS with voice: {voice_name}")

            # Check if ElevenLabs client is available
            if not elevenlabs_client.is_available:
                print("ElevenLabs API key not set. Falling back to OpenAI TTS.")

                # Temporarily switch to OpenAI if needed
                if current_provider != "openai" and ai_client.openai_client:
                    print(f"Temporarily switching to OpenAI for TTS (from {current_provider})")
                    ai_client.set_provider("openai")
                    provider_switched = True
                    client = ai_client.get_current_client()

                if ai_client.openai_client:
                    result = client.audio.speech.create(
                        model="tts-1",
                        voice="alloy",  # Default to alloy for fallback
                        input=cleaned_text,
                        speed=speech_rate,
                        response_format="mp3"
                    )

                    # Save the audio content to the output file
                    with open(output_file, "wb") as audio_file:
                        audio_file.write(result.content)

                    print(f"OpenAI speech synthesized (fallback from ElevenLabs) and saved to [{output_file}]")

                    # Restore the original provider if we switched
                    if provider_switched:
                        print(f"Restoring original provider: {current_provider}")
                        ai_client.set_provider(current_provider)

                    return True
                else:
                    raise Exception("OpenAI client not available for ElevenLabs fallback. Please set OPENAI_API_KEY in .env file.")

            # Get ElevenLabs model from config
            try:
                elevenlabs_config = config.get("elevenlabs", {})
                model = elevenlabs_config.get("model", "eleven_multilingual_v2")
                voice_settings = elevenlabs_config.get("voice_settings", {})

                stability = voice_settings.get("stability", 0.5)
                similarity = voice_settings.get("similarity_boost", 0.75)
                style = voice_settings.get("style", 0.0)
                speaker_boost = voice_settings.get("use_speaker_boost", True)

                print(f"Using ElevenLabs model: {model}")
            except Exception as e:
                print(f"Error loading ElevenLabs config: {str(e)}. Using defaults.")
                model = "eleven_multilingual_v2"
                stability = 0.5
                similarity = 0.75
                style = 0.0
                speaker_boost = True

            # Use ElevenLabs for TTS
            success = elevenlabs_client.generate_audio(
                text=cleaned_text,
                voice_id=voice_name,
                output_file=output_file,
                stability=stability,
                similarity=similarity,
                style=style,
                speaker_boost=speaker_boost,
                model=model
            )

            if success:
                print(f"ElevenLabs speech synthesized and saved to [{output_file}]")
                return True
            else:
                raise Exception("Error generating audio with ElevenLabs TTS")

        # If using Groq TTS model (not yet implemented, fall back to OpenAI)
        elif tts_model == "Groq":
            print(f"Using Groq TTS model selection, but Groq TTS is not yet available, falling back to OpenAI")

            # Temporarily switch to OpenAI if needed
            if current_provider != "openai" and ai_client.openai_client:
                print(f"Temporarily switching to OpenAI for TTS (from {current_provider})")
                ai_client.set_provider("openai")
                provider_switched = True
                client = ai_client.get_current_client()

            if ai_client.openai_client:
                result = client.audio.speech.create(
                    model="tts-1",
                    voice=voice_name,
                    input=cleaned_text,
                    speed=speech_rate,
                    response_format="mp3"
                )

                # Save the audio content to the output file
                with open(output_file, "wb") as audio_file:
                    audio_file.write(result.content)

                print(f"OpenAI speech synthesized (fallback from Groq TTS model) and saved to [{output_file}]")

                # Restore the original provider if we switched
                if provider_switched:
                    print(f"Restoring original provider: {current_provider}")
                    ai_client.set_provider(current_provider)

                return True
            else:
                raise Exception("OpenAI client not available for Groq fallback. Please set OPENAI_API_KEY in .env file.")

        print(f"Speech synthesized and the audio was saved to [{output_file}]")
        return True

    except Exception as e:
        print(f"Error generating audio: {str(e)}")

        # Restore the original provider if we switched and an error occurred
        if provider_switched:
            try:
                print(f"Restoring original provider after error: {current_provider}")
                ai_client.set_provider(current_provider)
            except Exception as restore_error:
                print(f"Error restoring original provider: {str(restore_error)}")

        raise Exception(f"Error generating audio: {str(e)}")  # Re-raise for better error handling
