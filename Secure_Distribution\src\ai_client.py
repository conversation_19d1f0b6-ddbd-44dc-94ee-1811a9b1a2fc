import os
import logging
from openai import OpenAI
from groq import Groq  # For Groq support

logger = logging.getLogger(__name__)

# Global client instances
openai_client = None
groq_client = None
elevenlabs_api_key = None
current_provider = "groq"  # Default provider

# Groq model mappings (OpenAI model name -> Groq model name)
GROQ_MODEL_MAPPING = {
    # GPT-3.5 models
    "gpt-3.5-turbo": "llama3-8b-8192",      # LLaMA 3 8B as alternative (faster)
    "gpt-3.5-turbo-16k": "llama3-8b-8192",  # LLaMA 3 8B with context

    # GPT-4 models
    "gpt-4": "llama3-70b-8192",             # LLaMA 3 70B as alternative to GPT-4
    "gpt-4-turbo": "llama3-70b-8192",       # LLaMA 3 70B as alternative
    "gpt-4o": "llama3-70b-8192",            # LLaMA 3 70B as alternative
    "gpt-4o-mini": "llama3-8b-8192",        # LLaMA 3 8B as smaller alternative
    "gpt-4-vision": "llama3-70b-8192",      # Vision not directly supported

    # Speech-to-Text models
    "whisper-1": "whisper-large-v3-turbo",  # Groq's latest Whisper model for transcription

    # Text-to-Speech models
    "tts-1": "tts-1"                        # Using same name as OpenAI for compatibility
}

# Default model to use if no mapping is found
DEFAULT_GROQ_MODEL = "llama3-8b-8192"  # Use LLaMA 3 8B as default fallback

def initialize_clients():
    """Initialize OpenAI, Groq, and ElevenLabs clients"""
    global openai_client, groq_client, elevenlabs_api_key

    # Initialize OpenAI client if API key is available
    openai_api_key = os.getenv("OPENAI_API_KEY")
    if openai_api_key:
        openai_client = OpenAI(
            api_key=openai_api_key,
            base_url=os.getenv("OPENAI_BASE_URL"),
        )
        logger.info("OpenAI client initialized")
    else:
        logger.warning("OpenAI API key not found. OpenAI features will not be available.")

    # Initialize Groq client if API key is available
    groq_api_key = os.getenv("GROQ_API_KEY")
    if groq_api_key:
        groq_client = Groq(api_key=groq_api_key)
        logger.info("Groq client initialized")
    else:
        logger.warning("Groq API key not found. Groq features will not be available.")

    # Store ElevenLabs API key if available
    elevenlabs_api_key = os.getenv("ELEVENLABS_API_KEY")
    if elevenlabs_api_key:
        logger.info("ElevenLabs API key found")
    else:
        logger.warning("ElevenLabs API key not found. ElevenLabs TTS features will not be available.")

def set_provider(provider):
    """Set the AI provider to use (openai, groq, or elevenlabs)"""
    global current_provider

    if provider not in ["openai", "groq", "elevenlabs"]:
        raise ValueError("Provider must be 'openai', 'groq', or 'elevenlabs'")

    if provider == "groq" and groq_client is None:
        raise ValueError("Groq client not initialized. Please check your GROQ_API_KEY.")

    if provider == "openai" and openai_client is None:
        raise ValueError("OpenAI client not initialized. Please check your OPENAI_API_KEY.")

    if provider == "elevenlabs" and not elevenlabs_api_key:
        raise ValueError("ElevenLabs API key not initialized. Please check your ELEVENLABS_API_KEY.")

    current_provider = provider
    logger.info(f"Set AI provider to {provider}")
    return True

def get_current_client():
    """Get the current AI provider client"""
    if current_provider == "openai":
        if openai_client is None:
            logger.error("OpenAI client is not initialized but was requested")
            # Fall back to Groq if available
            if groq_client is not None:
                logger.warning("Falling back to Groq client")
                return groq_client
            else:
                logger.error("No AI clients are available")
                return None
        return openai_client
    elif current_provider == "elevenlabs":
        # For ElevenLabs, we don't return a client here since we use the elevenlabs package directly
        # Instead, we check if the API key is set
        if not elevenlabs_api_key:
            logger.error("ElevenLabs API key is not set but was requested")
            return None
        # Return None to indicate that we should use the elevenlabs package directly
        return None
    else:  # groq
        if groq_client is None:
            logger.error("Groq client is not initialized but was requested")
            # Fall back to OpenAI if available
            if openai_client is not None:
                logger.warning("Falling back to OpenAI client")
                return openai_client
            else:
                logger.error("No AI clients are available")
                return None
        return groq_client

def get_current_provider():
    """Get the name of the current AI provider"""
    return current_provider

def get_appropriate_model(model_name):
    """Get the appropriate model name for the current provider"""
    if current_provider == "openai":
        return model_name

    # For Groq, map the OpenAI model to a Groq model
    if model_name in GROQ_MODEL_MAPPING:
        return GROQ_MODEL_MAPPING[model_name]

    # If no mapping exists, use the default Groq model
    logger.warning(f"No Groq mapping found for model {model_name}. Using default model {DEFAULT_GROQ_MODEL}")
    return DEFAULT_GROQ_MODEL

def chat_completion(messages, model="gpt-3.5-turbo", temperature=0.7, max_tokens=None, stream=False):
    """Generic chat completion function that works with both OpenAI and Groq

    Args:
        messages (list): List of message dictionaries with 'role' and 'content'
        model (str): Model name (will be mapped to appropriate provider model)
        temperature (float): Temperature for generation
        max_tokens (int): Maximum tokens to generate
        stream (bool): Whether to stream the response

    Returns:
        The completion response from the AI provider
    """
    client = get_current_client()
    provider_model = get_appropriate_model(model)

    try:
        if current_provider == "openai":
            return client.chat.completions.create(
                model=provider_model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=stream
            )
        else:  # groq
            # Groq uses 'max_completion_tokens' instead of 'max_tokens'
            return client.chat.completions.create(
                model=provider_model,
                messages=messages,
                temperature=temperature,
                max_completion_tokens=max_tokens,
                stream=stream
            )
    except Exception as e:
        logger.error(f"Error in chat completion with {current_provider}: {str(e)}")
        raise

def transcribe_audio(audio_file, prompt=None, response_format="text"):
    """Transcribe audio file using the current provider

    Args:
        audio_file (str): Path to audio file
        prompt (str): Optional prompt to guide transcription
        response_format (str): Format of the response ("text" or "verbose_json")

    Returns:
        Transcription text or JSON object with word-level timestamps
    """
    client = get_current_client()

    try:
        if current_provider == "openai":
            # OpenAI Whisper transcription
            with open(audio_file, "rb") as f:
                transcription = client.audio.transcriptions.create(
                    model="whisper-1",
                    file=f,
                    prompt=prompt,
                    response_format="verbose_json" if response_format == "verbose_json" else "text",
                    timestamp_granularities=["word"] if response_format == "verbose_json" else None
                )

            # Return the appropriate format
            if response_format == "verbose_json":
                return transcription
            else:
                return transcription.text
        else:
            # Groq transcription
            logger.info(f"Using Groq for audio transcription: {audio_file}")

            # Ensure we have the Groq client
            if groq_client is None:
                raise ValueError("Groq client not initialized. Please check your GROQ_API_KEY.")

            # Call Groq's speech-to-text API
            # Based on Groq API documentation: https://console.groq.com/docs/speech-to-text
            try:
                # Open the audio file as bytes for Groq API
                with open(audio_file, "rb") as f:
                    audio_data = f.read()

                # For word-level timestamps (verbose_json)
                if response_format == "verbose_json":
                    logger.info(f"Requesting Groq transcription with word timestamps")

                    # Implement retry logic with exponential backoff
                    max_retries = 5  # Maximum number of retry attempts
                    transcription = None
                    word_level_success = False

                    # First try with word-level timestamps (verbose_json format)
                    for retry_attempt in range(max_retries):
                        try:
                            logger.info(f"Attempt {retry_attempt + 1}/{max_retries} for Groq word-level transcription")
                            transcription = groq_client.audio.transcriptions.create(
                                file=("audio.mp3", audio_data),  # Pass as tuple (filename, bytes)
                                model="whisper-large-v3-turbo",  # Using Groq's latest model
                                response_format="verbose_json",
                                timestamp_granularities=["word", "segment"]  # Need both for complete data
                            )
                            logger.info(f"Groq transcription successful with word timestamps")
                            word_level_success = True
                            break  # Success, exit retry loop
                        except Exception as e:
                            error_message = str(e)
                            logger.warning(f"Error with verbose_json format (attempt {retry_attempt + 1}/{max_retries}): {error_message}")

                            # Check if this is a server error (like 503) that warrants a retry
                            is_server_error = "503" in error_message or "server error" in error_message.lower() or "timeout" in error_message.lower()

                            if retry_attempt < max_retries - 1 and is_server_error:
                                # Calculate backoff time: 1s, 2s, 4s, 8s...
                                backoff_time = 2 ** retry_attempt
                                logger.info(f"Retrying in {backoff_time} seconds...")
                                import time
                                time.sleep(backoff_time)
                            elif retry_attempt == max_retries - 1:
                                # Last attempt failed, log and continue to fallback
                                logger.warning(f"All {max_retries} attempts for word-level transcription failed")

                    # If word-level transcription failed after all retries, fall back to text-only
                    if not word_level_success:
                        logger.info("Falling back to text-only transcription")
                        try:
                            # Fall back to text-only transcription
                            transcription = groq_client.audio.transcriptions.create(
                                file=("audio.mp3", audio_data),
                                model="whisper-large-v3-turbo"
                            )
                            logger.info(f"Groq text-only transcription successful")
                        except Exception as text_error:
                            logger.warning(f"Error with text-only transcription: {str(text_error)}")
                            # Let the outer exception handler deal with the fallback to OpenAI
                            raise

                    # Process the response to ensure it has the expected format
                    # Convert to a dictionary if it's not already
                    if not isinstance(transcription, dict):
                        try:
                            # Try to convert to dict if it's a model
                            if hasattr(transcription, 'model_dump'):
                                transcription_dict = transcription.model_dump()
                                logger.info("Converted transcription using model_dump()")
                            elif hasattr(transcription, '__dict__'):
                                transcription_dict = transcription.__dict__
                                logger.info("Converted transcription using __dict__")
                            else:
                                # Try to access attributes directly
                                transcription_dict = {}
                                if hasattr(transcription, 'text'):
                                    transcription_dict['text'] = transcription.text
                                    logger.info(f"Extracted text attribute: {transcription.text[:50]}...")
                                if hasattr(transcription, 'words'):
                                    transcription_dict['words'] = transcription.words
                                    logger.info(f"Extracted words attribute with {len(transcription.words)} words")
                                if hasattr(transcription, 'segments'):
                                    transcription_dict['segments'] = transcription.segments
                                    logger.info(f"Extracted segments attribute")
                        except Exception as e:
                            logger.warning(f"Error converting transcription to dict: {str(e)}")
                            # If conversion fails, try to parse as JSON
                            try:
                                import json
                                transcription_dict = json.loads(str(transcription))
                                logger.info("Converted transcription using json.loads()")
                            except Exception as e2:
                                logger.warning(f"Error parsing transcription as JSON: {str(e2)}")
                                # Last resort: create a basic dict with the text
                                if hasattr(transcription, 'text'):
                                    text = transcription.text
                                else:
                                    text = str(transcription)
                                transcription_dict = {"text": text}
                                logger.info(f"Created basic dict with text: {text[:50]}...")
                    else:
                        transcription_dict = transcription
                        logger.info(f"Transcription is already a dict with keys: {', '.join(transcription_dict.keys())}")

                    # Debug: Print the structure of the transcription
                    logger.info(f"Transcription dict keys: {', '.join(transcription_dict.keys())}")
                    if 'words' in transcription_dict:
                        logger.info(f"Found 'words' key with {len(transcription_dict['words'])} words")
                    if 'segments' in transcription_dict:
                        logger.info(f"Found 'segments' key with {len(transcription_dict['segments'])} segments")
                    if 'text' in transcription_dict:
                        logger.info(f"Found 'text' key with content: {transcription_dict['text'][:50]}...")

                    # Ensure the response has a 'words' key with proper word-level timestamps
                    if 'words' not in transcription_dict and 'segments' in transcription_dict:
                        # Extract words from segments
                        words = []
                        for segment in transcription_dict['segments']:
                            if 'words' in segment:
                                words.extend(segment['words'])
                                logger.info(f"Extracted {len(segment['words'])} words from segment")

                        if words:
                            transcription_dict['words'] = words
                            logger.info(f"Created 'words' key with {len(words)} words from segments")

                    # If still no words, create basic word structure from text
                    if ('words' not in transcription_dict or not transcription_dict['words']) and 'text' in transcription_dict:
                        text = transcription_dict['text']
                        logger.info(f"Creating word-level data from text: {text[:50]}...")

                        # Try to get video duration from the transcription
                        duration = None
                        if 'duration' in transcription_dict:
                            duration = transcription_dict['duration']
                            logger.info(f"Using duration from transcription: {duration:.2f} seconds")

                        words = []
                        text_words = text.split()
                        logger.info(f"Text contains {len(text_words)} words")

                        # Use duration if available, otherwise estimate
                        total_duration = duration if duration else max(5.0, len(text_words) * 0.5)
                        time_per_word = total_duration / len(text_words) if text_words else 0.5
                        logger.info(f"Using estimated duration: {total_duration:.2f} seconds ({time_per_word:.2f}s per word)")

                        for i, word in enumerate(text_words):
                            start_time = i * time_per_word
                            end_time = (i + 1) * time_per_word

                            # Add a small buffer between words for more natural timing
                            if i > 0:
                                start_time -= 0.05
                            if i < len(text_words) - 1:
                                end_time += 0.05

                            words.append({
                                "word": word,
                                "start": start_time,
                                "end": end_time,
                                "text": word
                            })
                        transcription_dict['words'] = words
                        logger.info(f"Created {len(words)} word timings from text")

                    logger.info(f"Processed Groq transcription with {len(transcription_dict.get('words', []))} words")
                    return transcription_dict
                else:
                    # For simple text transcription
                    logger.info(f"Requesting Groq transcription (text only)")

                    # Implement retry logic with exponential backoff
                    max_retries = 5  # Maximum number of retry attempts

                    for retry_attempt in range(max_retries):
                        try:
                            logger.info(f"Attempt {retry_attempt + 1}/{max_retries} for Groq text-only transcription")
                            transcription = groq_client.audio.transcriptions.create(
                                file=("audio.mp3", audio_data),  # Pass as tuple (filename, bytes)
                                model="whisper-large-v3-turbo"  # Using Groq's latest model
                            )
                            logger.info(f"Groq transcription successful: {transcription.text[:50]}...")
                            return transcription.text
                        except Exception as e:
                            error_message = str(e)
                            logger.warning(f"Error with text-only transcription (attempt {retry_attempt + 1}/{max_retries}): {error_message}")

                            # Check if this is a server error (like 503) that warrants a retry
                            is_server_error = "503" in error_message or "server error" in error_message.lower() or "timeout" in error_message.lower()

                            if retry_attempt < max_retries - 1 and is_server_error:
                                # Calculate backoff time: 1s, 2s, 4s, 8s...
                                backoff_time = 2 ** retry_attempt
                                logger.info(f"Retrying in {backoff_time} seconds...")
                                import time
                                time.sleep(backoff_time)
                            elif retry_attempt == max_retries - 1:
                                # Last attempt failed, log and continue to fallback
                                logger.warning(f"All {max_retries} attempts for text-only transcription failed")
                                # Let the outer exception handler deal with the fallback to OpenAI
                                raise Exception(f"Groq text-only transcription failed after {max_retries} attempts")
            except Exception as groq_error:
                # Log the specific Groq error
                logger.error(f"Groq transcription error: {str(groq_error)}")

                # Fall back to OpenAI if available
                if openai_client:
                    logger.warning("Falling back to OpenAI for transcription after Groq error")
                    with open(audio_file, "rb") as f:
                        transcription = openai_client.audio.transcriptions.create(
                            model="whisper-1",
                            file=f,
                            prompt=prompt,
                            response_format="verbose_json" if response_format == "verbose_json" else "text",
                            timestamp_granularities=["word"] if response_format == "verbose_json" else None
                        )

                    # Return the appropriate format
                    if response_format == "verbose_json":
                        return transcription
                    else:
                        return transcription.text
                else:
                    # Re-raise the original error if we can't fall back
                    raise groq_error
    except Exception as e:
        logger.error(f"Error in audio transcription with {current_provider}: {str(e)}")
        raise

def text_to_speech(text, voice="alloy", model="tts-1"):
    """Convert text to speech using the current provider

    Args:
        text (str): Text to convert to speech
        voice (str): Voice to use
        model (str): Model to use (for ElevenLabs)

    Returns:
        Audio content
    """
    client = get_current_client()

    try:
        if current_provider == "openai":
            # OpenAI TTS
            response = client.audio.speech.create(
                model="tts-1",
                voice=voice,
                input=text
            )
            return response.content
        elif current_provider == "elevenlabs":
            # ElevenLabs TTS
            from elevenlabs_client import elevenlabs_client

            # Check if ElevenLabs client is available
            if not elevenlabs_client.is_available:
                logger.warning("ElevenLabs API key not set. Cannot generate audio.")
                raise ValueError("ElevenLabs API key not set. Please set ELEVENLABS_API_KEY in .env file.")

            # Create a temporary file to store the audio
            import tempfile
            import os

            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".mp3")
            temp_file.close()

            try:
                # Generate audio using ElevenLabs client
                success = elevenlabs_client.generate_audio(
                    text=text,
                    voice_id=voice,  # voice parameter is the voice_id
                    output_file=temp_file.name,
                    model=model
                )

                if not success:
                    raise ValueError("Failed to generate audio with ElevenLabs")

                # Read the audio file
                with open(temp_file.name, "rb") as f:
                    audio_content = f.read()

                return audio_content
            finally:
                # Clean up the temporary file
                try:
                    os.unlink(temp_file.name)
                except:
                    pass
        else:
            # Groq TTS (if implemented)
            logger.warning("Groq text-to-speech not implemented in this version")
            raise NotImplementedError("Groq text-to-speech not implemented in this version")
    except Exception as e:
        logger.error(f"Error in text-to-speech with {current_provider}: {str(e)}")
        raise

# Initialize clients on module import
initialize_clients()