#!/usr/bin/env python3
"""
Demo version of the Modern 1ClickVideo UI
This version bypasses authentication for testing and demonstration purposes
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def demo_main():
    """Demo version that bypasses authentication"""
    try:
        from ttkthemes import ThemedTk
        from modern_ui import ModernApp
        
        print("Starting Modern UI Demo...")
        print("Note: This demo bypasses authentication for testing purposes")
        
        # Create root window
        root = ThemedTk(theme="arc")
        root.title("1ClickVideo - Modern UI Demo")
        
        # Set window size and position
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        window_width = 1400
        window_height = 800
        
        center_x = int(screen_width/2 - window_width/2)
        center_y = int(screen_height/2 - window_height/2)
        
        root.geometry(f"{window_width}x{window_height}+{center_x}+{center_y}")
        root.minsize(1200, 700)
        
        # Show demo notice
        def show_demo_notice():
            messagebox.showinfo(
                "Demo Mode", 
                "Welcome to the Modern UI Demo!\n\n"
                "This is a demonstration of the new modern interface.\n"
                "All UI features are functional, but video generation\n"
                "requires proper authentication.\n\n"
                "Features you can explore:\n"
                "• Modern card-based layout\n"
                "• Voice selection and preview\n"
                "• Subtitle styling options\n"
                "• Custom content input\n"
                "• Real-time status logging\n\n"
                "Click OK to continue..."
            )
        
        # Show notice after a brief delay
        root.after(1000, show_demo_notice)
        
        # Create the modern application
        app = ModernApp(root)
        
        # Add demo watermark
        demo_label = tk.Label(
            root,
            text="DEMO MODE - Authentication Bypassed",
            bg="#FF6B6B",
            fg="white",
            font=("Arial", 10, "bold"),
            padx=10,
            pady=5
        )
        demo_label.place(relx=1.0, rely=0.0, anchor="ne")
        
        print("Modern UI Demo started successfully!")
        print("Close the window to exit the demo.")
        
        # Start the main loop
        root.mainloop()
        
    except Exception as e:
        print(f"Error starting demo: {e}")
        import traceback
        traceback.print_exc()
        
        # Show error in a simple dialog
        try:
            root = tk.Tk()
            root.withdraw()  # Hide the main window
            messagebox.showerror(
                "Demo Error", 
                f"Failed to start Modern UI Demo:\n\n{str(e)}\n\n"
                "Please check that all dependencies are installed."
            )
        except:
            pass

if __name__ == "__main__":
    demo_main()
