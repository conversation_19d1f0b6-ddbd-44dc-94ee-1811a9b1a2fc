#!/usr/bin/env python3
"""
Test script for the Modern UI Settings Dialog
"""

import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

import tkinter as tk
from ttkthemes import ThemedTk
from modern_ui import ModernApp, ModernSettingsDialog

def test_settings_dialog():
    """Test the settings dialog functionality"""
    try:
        print("Testing Modern UI Settings Dialog...")
        
        # Create root window
        root = ThemedTk(theme="arc")
        root.title("Settings Dialog Test")
        root.geometry("400x300")
        
        # Create a minimal app instance for testing
        app = ModernApp(root)
        
        # Create test button to open settings
        test_frame = tk.Frame(root, bg="#0F172A")
        test_frame.pack(fill=tk.BOTH, expand=True, padx=50, pady=50)
        
        title_label = tk.Label(
            test_frame,
            text="Settings Dialog Test",
            font=("Arial", 16, "bold"),
            fg="#F8FAFC",
            bg="#0F172A"
        )
        title_label.pack(pady=(0, 20))
        
        # Test button
        test_btn = tk.Button(
            test_frame,
            text="🔧 Open Settings Dialog",
            command=app.show_settings,
            bg="#6366F1",
            fg="white",
            font=("Arial", 12, "bold"),
            padx=20,
            pady=10,
            relief=tk.FLAT,
            cursor="hand2"
        )
        test_btn.pack(pady=10)
        
        # Instructions
        instructions = tk.Label(
            test_frame,
            text="Click the button above to test the settings dialog.\nCheck all tabs and functionality.",
            font=("Arial", 10),
            fg="#CBD5E1",
            bg="#0F172A",
            justify=tk.CENTER
        )
        instructions.pack(pady=(20, 0))
        
        print("Settings dialog test window created successfully!")
        print("Click the 'Open Settings Dialog' button to test the functionality.")
        print("Close the window to exit the test.")
        
        # Start the main loop
        root.mainloop()
        
    except Exception as e:
        print(f"Error testing settings dialog: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_settings_dialog()
