import base64, zlib; exec(zlib.decompress(base64.b64decode('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')).decode('utf-8'))