@echo off 
setlocal EnableDelayedExpansion 
 
echo ======================================================= 
echo   Running Modern 1ClickVideo v6.5 
echo ======================================================= 
echo. 
 
:: Check if venv exists and activate it 
if exist "venv\Scripts\activate.bat" ( 
    echo Activating virtual environment... 
    call venv\Scripts\activate.bat 
    echo Virtual environment activated successfully. 
) else ( 
    echo No virtual environment found. Running with system Python... 
) 
 
:: Create default config file if it doesn't exist 
if not exist "config.json" ( 
    echo Creating default config file... 
    echo { 
    echo   "spreadsheet_id": "1kOOVtONxmaVQa2qnuSWcLP-82kmbZDVKGc2ljOfiZ10", 
    echo   "whatsapp_number": "+923107520004", 
    echo   "tts": { 
    echo     "speech_rate": 1.0 
    echo   } 
    echo } 
) 
 
:: Check for MoviePy installation 
python -c "import moviepy" 
if errorlevel 1 ( 
    echo MoviePy package not found. Installing it now... 
    pip install moviepy 
    if errorlevel 1 ( 
        echo Failed to install MoviePy. Trying alternate method... 
        pip install --upgrade pip 
        pip install moviepy==1.0.3 decorator==4.4.2 imageio==2.35.1 
        if errorlevel 1 ( 
            echo ERROR: Could not install MoviePy. Please install it manually by running: 
            echo pip install moviepy 
            pause 
        ) else ( 
            echo MoviePy installed successfully 
        ) 
    ) else ( 
        echo MoviePy installed successfully 
    ) 
) 
 
echo Setting up Python path... 
set PYTHONPATH=D:\opensource projects\1ClickVideo 
echo Starting Modern UI application... 
python launch_modern_ui.py 
 
echo Application closed. Press any key to exit... 
pause 
