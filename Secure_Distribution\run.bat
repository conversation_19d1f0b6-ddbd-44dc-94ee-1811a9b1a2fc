@echo off
setlocal EnableDelayedExpansion
title Modern 1ClickVideo v6.5 - Contemporary UI

echo =====================================================
echo   Modern 1ClickVideo v6.5 - Contemporary UI
echo =====================================================
echo.
echo Starting application with modern design interface...
echo.

:: Check if venv exists and activate it
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
    echo Virtual environment activated successfully.
) else (
    echo No virtual environment found. Running with system Python...
)
echo.

:: Check for Modern UI dependencies
echo Checking Modern UI dependencies...
python -c "import ttkthemes" 2>nul
if errorlevel 1 (
    echo ttkthemes package not found. Installing it now...
    pip install ttkthemes>=3.2.2
    if errorlevel 1 (
        echo Failed to install ttkthemes. Please install it manually by running:
        echo pip install ttkthemes>=3.2.2
        pause
        exit /b 1
    ) else (
        echo ttkthemes installed successfully!
    )
)

python -c "import PIL" 2>nul
if errorlevel 1 (
    echo Pillow package not found. Installing it now...
    pip install Pillow>=10.2.0
    if errorlevel 1 (
        echo Failed to install Pillow. Please install it manually by running:
        echo pip install Pillow>=10.2.0
        pause
        exit /b 1
    ) else (
        echo Pillow installed successfully!
    )
)

:: Check for MoviePy installation
python -c "import moviepy" 2>nul
if errorlevel 1 (
    echo MoviePy package not found. Installing it now...
    pip install moviepy
    if errorlevel 1 (
        echo Failed to install MoviePy. Trying alternate method...
        pip install --upgrade pip
        pip install moviepy==1.0.3 decorator==4.4.2 imageio==2.35.1
        if errorlevel 1 (
            echo ERROR: Could not install MoviePy. Please install it manually by running:
            echo pip install moviepy
            pause
            exit /b 1
        ) else (
            echo MoviePy installed successfully!
        )
    ) else (
        echo MoviePy installed successfully!
    )
)
echo.

:: Create default config file if it doesn't exist
if not exist "config.json" (
    echo Creating default config file...
    echo { > "config.json"
    echo   "spreadsheet_id": "1kOOVtONxmaVQa2qnuSWcLP-82kmbZDVKGc2ljOfiZ10", >> "config.json"
    echo   "whatsapp_number": "+923107520004", >> "config.json"
    echo   "tts": { >> "config.json"
    echo     "speech_rate": 1.0 >> "config.json"
    echo   } >> "config.json"
    echo } >> "config.json"
)
echo.

echo Setting up Python path...
set PYTHONPATH=%CD%
echo Starting Modern UI application...
echo.
echo Features:
echo - Contemporary Material Design interface
echo - Modern color palette and typography
echo - Sleek buttons with hover effects
echo - Organized tab navigation
echo - Real-time status monitoring
echo.

python launch_modern_ui.py

echo.
echo Modern UI application closed. Press any key to exit...
pause
